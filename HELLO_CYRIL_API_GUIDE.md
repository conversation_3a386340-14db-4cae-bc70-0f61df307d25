# 🚀 Hello Cyril API - Complete Integration Guide

## 📋 Overview

The Hello Cyril API provides real-time access to crime, EMS, and infrastructure reports across South Africa. This guide covers everything you need to integrate with our API, including REST endpoints, WebSocket connections, and webhook notifications.

## 🔑 Getting Started

### 1. Get Your API Key

1. **Subscribe to a Plan**: Visit [hellocyril.co.za/subscription](https://hellocyril.co.za/subscription)
2. **Choose Your Tier**:
   - **Basic** (R99/month): 10K requests, basic features
   - **Premium** (R299/month): 50K requests, real-time alerts
   - **Enterprise** (R999/month): 200K requests, webhooks, SLA
3. **Complete Payment**: Secure payment via Yoco
4. **Get API Key**: Instantly generated after successful payment

### 2. API Key Retrieval

Lost your API key? Retrieve it at [hellocyril.co.za/api-key-retrieval](https://hellocyril.co.za/api-key-retrieval) using your email and contact name.

## 🔐 Authentication

All API requests require your API key in the header:

```bash
X-API-Key: hc_your_api_key_here
```

## 📡 API Endpoints

### Base URL
```
https://hellocyril.co.za/api/subscription/
```

### 1. Get Reports
```http
GET /reports
```

**Parameters:**
- `category` (optional): crime, ems, infrastructure
- `start_date` (optional): ISO format date
- `end_date` (optional): ISO format date
- `limit` (optional): 1-1000 (default: 100)
- `offset` (optional): Pagination offset

**Example:**
```bash
curl -H "X-API-Key: hc_your_key" \
  "https://hellocyril.co.za/api/subscription/reports?category=crime&limit=50"
```

### 2. Get Statistics
```http
GET /stats
```

**Parameters:**
- `period` (optional): overall, daily, weekly, monthly

**Example:**
```bash
curl -H "X-API-Key: hc_your_key" \
  "https://hellocyril.co.za/api/subscription/stats?period=weekly"
```

## 🔄 Real-Time Features

### WebSocket Connection (Premium/Enterprise)

Connect to real-time alerts:

```javascript
const ws = new WebSocket('wss://hellocyril.co.za/api/subscription/ws?api_key=hc_your_key');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'alert') {
        console.log('New alert:', data.data);
    }
};
```

### Webhooks (Enterprise Only)

Subscribe to webhook notifications:

```bash
curl -X POST \
  -H "X-API-Key: hc_your_key" \
  -H "Content-Type: application/json" \
  -d '{
    "webhook_url": "https://your-app.com/webhook",
    "alert_types": ["crime", "ems"],
    "geographic_filter": {
      "center": {"latitude": -25.7461, "longitude": 28.1881},
      "radius_km": 10
    }
  }' \
  "https://hellocyril.co.za/api/subscription/webhooks/subscribe"
```

## 💻 Code Examples

### JavaScript/Node.js

```javascript
class HelloCyrilAPI {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseURL = 'https://hellocyril.co.za/api/subscription';
    }
    
    async getReports(options = {}) {
        const params = new URLSearchParams(options);
        const response = await fetch(`${this.baseURL}/reports?${params}`, {
            headers: { 'X-API-Key': this.apiKey }
        });
        return response.json();
    }
    
    connectWebSocket() {
        const ws = new WebSocket(`wss://hellocyril.co.za/api/subscription/ws?api_key=${this.apiKey}`);
        
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };
        
        return ws;
    }
    
    handleMessage(data) {
        switch(data.type) {
            case 'alert':
                console.log('New alert:', data.data);
                break;
            case 'connection':
                console.log('Connected to Hello Cyril API');
                break;
        }
    }
}

// Usage
const api = new HelloCyrilAPI('hc_your_api_key');
const reports = await api.getReports({category: 'crime', limit: 10});
const ws = api.connectWebSocket();
```

### Python

```python
import requests
import websocket
import json

class HelloCyrilAPI:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = 'https://hellocyril.co.za/api/subscription'
        self.headers = {'X-API-Key': api_key}
    
    def get_reports(self, **params):
        response = requests.get(
            f'{self.base_url}/reports',
            headers=self.headers,
            params=params
        )
        return response.json()
    
    def get_stats(self, period='overall'):
        response = requests.get(
            f'{self.base_url}/stats',
            headers=self.headers,
            params={'period': period}
        )
        return response.json()
    
    def subscribe_webhook(self, webhook_url, alert_types=['all']):
        data = {
            'webhook_url': webhook_url,
            'alert_types': alert_types
        }
        response = requests.post(
            f'{self.base_url}/webhooks/subscribe',
            headers=self.headers,
            json=data
        )
        return response.json()

# WebSocket client
def on_message(ws, message):
    data = json.loads(message)
    if data.get('type') == 'alert':
        print(f"New alert: {data['data']}")

def connect_websocket(api_key):
    ws_url = f"wss://hellocyril.co.za/api/subscription/ws?api_key={api_key}"
    ws = websocket.WebSocketApp(ws_url, on_message=on_message)
    ws.run_forever()

# Usage
api = HelloCyrilAPI('hc_your_api_key')
reports = api.get_reports(category='crime', limit=50)
stats = api.get_stats(period='weekly')
```

## 🚨 Error Handling

### Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 401 | Invalid API key | Check your API key in X-API-Key header |
| 429 | Rate limit exceeded | Reduce request frequency or upgrade plan |
| 403 | Feature not available | Upgrade to Premium/Enterprise |
| 400 | Bad request | Check request parameters |

### Error Response Format

```json
{
  "detail": "Invalid API key",
  "status_code": 401,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 📊 Rate Limits

### Request Limits

| Plan | Monthly Requests | Rate Limit | Features |
|------|------------------|------------|----------|
| Basic | 10,000 | 100/min | Reports, Stats |
| Premium | 50,000 | 500/min | + WebSocket |
| Enterprise | 200,000 | 2,000/min | + Webhooks |

### Best Practices

1. **Implement exponential backoff** for rate limit errors
2. **Cache responses** when possible
3. **Use WebSocket** for real-time data instead of polling
4. **Monitor usage** via the usage endpoint

## 🔧 Integration Examples

### Security App Integration

```javascript
// Monitor crime reports in real-time
const api = new HelloCyrilAPI('hc_your_key');

// Get recent crime reports
const crimeReports = await api.getReports({
    category: 'crime',
    start_date: new Date(Date.now() - 24*60*60*1000).toISOString()
});

// Connect to real-time alerts
const ws = api.connectWebSocket();
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'alert' && data.data.category === 'crime') {
        sendSecurityAlert(data.data);
    }
};
```

### Insurance Risk Assessment

```python
# Analyze crime patterns for insurance risk
api = HelloCyrilAPI('hc_your_key')

# Get historical crime data
crime_data = api.get_reports(
    category='crime',
    start_date='2024-01-01T00:00:00Z',
    limit=1000
)

# Calculate risk score for location
def calculate_risk_score(latitude, longitude, radius_km=5):
    nearby_crimes = [
        report for report in crime_data['reports']
        if distance(latitude, longitude, 
                   report['location']['latitude'], 
                   report['location']['longitude']) <= radius_km
    ]
    return len(nearby_crimes) / 30  # Risk per day
```

## 📞 Support

### Documentation
- **API Docs**: [hellocyril.co.za/api-docs](https://hellocyril.co.za/api-docs)
- **Subscription Plans**: [hellocyril.co.za/subscription](https://hellocyril.co.za/subscription)

### Contact Support
- **Email**: Via [hellocyril.co.za/feedback](https://hellocyril.co.za/feedback)
- **Response Time**: 
  - Basic: 48 hours
  - Premium: 24 hours
  - Enterprise: 4 hours + SLA

### API Key Management
- **Retrieve Lost Key**: [hellocyril.co.za/api-key-retrieval](https://hellocyril.co.za/api-key-retrieval)
- **Key Expiry**: 1 year (automatic renewal with active subscription)

## 🚀 Getting Started Checklist

- [ ] Subscribe to a plan at [hellocyril.co.za/subscription](https://hellocyril.co.za/subscription)
- [ ] Save your API key securely
- [ ] Test basic API calls with your key
- [ ] Implement error handling and rate limiting
- [ ] Set up WebSocket connection (Premium+)
- [ ] Configure webhooks (Enterprise)
- [ ] Monitor usage and performance
- [ ] Contact support if needed

## 📈 Advanced Features

### Geographic Filtering (Enterprise)

```json
{
  "geographic_filter": {
    "bounds": {
      "north": -25.7,
      "south": -25.8,
      "east": 28.2,
      "west": 28.1
    }
  }
}
```

### Custom Alert Types

```json
{
  "alert_types": ["crime", "ems"],
  "severity_filter": "high"
}
```

---

**Ready to integrate?** Start with our [subscription plans](https://hellocyril.co.za/subscription) and get your API key today! 🚀
