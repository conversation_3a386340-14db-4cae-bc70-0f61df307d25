from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID

from app.models.database import get_db
from app.models.admin import Admin
from app.models.feedback import Feedback
from app.models.group import CommunityGroup
from app.schemas.admin import AdminCreate, AdminUpdate, AdminResponse
from app.api.auth import get_current_active_admin

router = APIRouter()

@router.get("/admins", response_model=List[AdminResponse])
async def get_all_admins(
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Get all admin users"""
    admins = db.query(Admin).all()
    return admins

@router.post("/admins", response_model=AdminResponse)
async def create_admin(
    admin_data: AdminCreate,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Create a new admin user"""
    # Check if username already exists
    if db.query(Admin).filter(Admin.username == admin_data.username).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )

    # Check if email already exists
    if db.query(Admin).filter(Admin.email == admin_data.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Create new admin
    admin = Admin(
        username=admin_data.username,
        email=admin_data.email
    )
    admin.set_password(admin_data.password)

    db.add(admin)
    db.commit()
    db.refresh(admin)

    return admin

@router.put("/admins/{admin_id}", response_model=AdminResponse)
async def update_admin(
    admin_id: UUID,
    admin_data: AdminUpdate,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Update an admin user"""
    admin = db.query(Admin).filter(Admin.id == admin_id).first()
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin not found"
        )

    # Update fields if provided
    if admin_data.username is not None:
        # Check if new username already exists (excluding current admin)
        existing = db.query(Admin).filter(
            Admin.username == admin_data.username,
            Admin.id != admin_id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )
        admin.username = admin_data.username

    if admin_data.email is not None:
        # Check if new email already exists (excluding current admin)
        existing = db.query(Admin).filter(
            Admin.email == admin_data.email,
            Admin.id != admin_id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )
        admin.email = admin_data.email

    if admin_data.password is not None:
        admin.set_password(admin_data.password)

    if admin_data.is_active is not None:
        admin.is_active = admin_data.is_active

    db.commit()
    db.refresh(admin)

    return admin

@router.delete("/admins/{admin_id}")
async def deactivate_admin(
    admin_id: UUID,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Deactivate an admin user"""
    admin = db.query(Admin).filter(Admin.id == admin_id).first()
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Admin not found"
        )

    # Prevent self-deactivation
    if admin.id == current_admin.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate your own account"
        )

    admin.is_active = False
    db.commit()

    return {"message": "Admin deactivated successfully"}

@router.get("/feedback")
async def get_all_feedback(
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Get all feedback submissions"""
    feedback = db.query(Feedback).order_by(Feedback.submitted_at.desc()).all()
    return feedback

@router.get("/feedback/{feedback_id}")
async def get_feedback_detail(
    feedback_id: UUID,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Get detailed feedback by ID"""
    feedback = db.query(Feedback).filter(Feedback.id == feedback_id).first()
    if not feedback:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Feedback not found"
        )
    return feedback

@router.put("/feedback/{feedback_id}")
async def update_feedback(
    feedback_id: UUID,
    title: str,
    comment: str,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Update feedback"""
    feedback = db.query(Feedback).filter(Feedback.id == feedback_id).first()
    if not feedback:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Feedback not found"
        )

    feedback.title = title
    feedback.comment = comment
    db.commit()
    db.refresh(feedback)

    return feedback

@router.delete("/feedback/{feedback_id}")
async def delete_feedback(
    feedback_id: UUID,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Delete feedback"""
    feedback = db.query(Feedback).filter(Feedback.id == feedback_id).first()
    if not feedback:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Feedback not found"
        )

    db.delete(feedback)
    db.commit()

    return {"message": "Feedback deleted successfully"}

@router.get("/groups")
async def get_all_groups(
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Get all community groups"""
    groups = db.query(CommunityGroup).all()
    return groups

@router.put("/groups/{group_id}/toggle-active")
async def toggle_group_active(
    group_id: UUID,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Toggle active status of a community group"""
    group = db.query(CommunityGroup).filter(CommunityGroup.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )

    group.active = not group.active
    db.commit()
    db.refresh(group)

    return {"message": f"Group {'activated' if group.active else 'deactivated'} successfully", "active": group.active}

@router.delete("/groups/{group_id}")
async def delete_group(
    group_id: UUID,
    current_admin: Admin = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """Delete a community group"""
    group = db.query(CommunityGroup).filter(CommunityGroup.id == group_id).first()
    if not group:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )

    db.delete(group)
    db.commit()

    return {"message": "Group deleted successfully"}
