from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
from typing import List, Optional
from datetime import datetime, timedelta
from uuid import UUID
import uuid

from app.models.database import get_db
from app.models.analytics import PageVisit, WhatsAppClick, DonationTracking
from app.api.auth import get_current_active_admin
from app.models.admin import Admin

router = APIRouter()

def get_client_ip(request: Request) -> str:
    """Extract client IP address from request"""
    # Check for X-Forwarded-For header (for clients behind proxies)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    # Check for X-Real-IP header
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    # Fall back to client host
    return request.client.host if request.client else "unknown"

def get_user_agent(request: Request) -> str:
    """Extract user agent from request"""
    return request.headers.get("User-Agent", "Unknown")

def get_session_id(request: Request) -> str:
    """Generate or get session ID"""
    # In a real implementation, you might use cookies or session management
    # For now, we'll generate a simple session ID based on IP + User Agent
    ip = get_client_ip(request)
    ua = get_user_agent(request)
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, f"{ip}_{ua}"))

# Public endpoints for tracking (no authentication required)
@router.post("/track/page-visit")
async def track_page_visit(
    request: Request,
    page_url: str,
    page_title: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Track a page visit"""
    try:
        visit = PageVisit(
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request),
            page_url=page_url,
            page_title=page_title,
            referrer=request.headers.get("Referer"),
            session_id=get_session_id(request)
        )
        
        db.add(visit)
        db.commit()
        
        return {"status": "success", "message": "Page visit tracked"}
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": "Failed to track page visit"}

@router.post("/track/whatsapp-click")
async def track_whatsapp_click(
    request: Request,
    click_source: str,
    whatsapp_number: Optional[str] = None,
    referrer_page: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Track a WhatsApp link click"""
    try:
        click = WhatsAppClick(
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request),
            click_source=click_source,
            whatsapp_number=whatsapp_number,
            referrer_page=referrer_page,
            session_id=get_session_id(request)
        )
        
        db.add(click)
        db.commit()
        
        return {"status": "success", "message": "WhatsApp click tracked"}
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": "Failed to track WhatsApp click"}

@router.post("/track/donation")
async def track_donation(
    request: Request,
    amount: float,
    payment_method: str,
    payment_status: str = "initiated",
    transaction_id: Optional[str] = None,
    payment_provider_id: Optional[str] = None,
    currency: str = "ZAR",
    db: Session = Depends(get_db)
):
    """Track a donation attempt or completion"""
    try:
        donation = DonationTracking(
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request),
            amount=amount,
            currency=currency,
            payment_method=payment_method,
            payment_status=payment_status,
            transaction_id=transaction_id,
            payment_provider_id=payment_provider_id,
            session_id=get_session_id(request)
        )
        
        if payment_status == "completed":
            donation.completion_time = datetime.utcnow()
        
        db.add(donation)
        db.commit()
        
        return {"status": "success", "message": "Donation tracked", "id": str(donation.id)}
    except Exception as e:
        db.rollback()
        return {"status": "error", "message": "Failed to track donation"}

@router.put("/track/donation/{donation_id}/update")
async def update_donation_status(
    donation_id: UUID,
    payment_status: str,
    payment_provider_id: Optional[str] = None,
    notes: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Update donation status (for payment completion callbacks)"""
    try:
        donation = db.query(DonationTracking).filter(DonationTracking.id == donation_id).first()
        if not donation:
            raise HTTPException(status_code=404, detail="Donation not found")
        
        donation.payment_status = payment_status
        if payment_provider_id:
            donation.payment_provider_id = payment_provider_id
        if notes:
            donation.notes = notes
        
        if payment_status == "completed" and not donation.completion_time:
            donation.completion_time = datetime.utcnow()
        
        db.commit()
        
        return {"status": "success", "message": "Donation status updated"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update donation status")

# Admin endpoints for viewing analytics
@router.get("/admin/page-visits")
async def get_page_visits(
    current_admin: Admin = Depends(get_current_active_admin),
    limit: int = 100,
    offset: int = 0,
    days: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Get page visit analytics"""
    query = db.query(PageVisit)
    
    if days:
        since_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(PageVisit.visit_time >= since_date)
    
    visits = query.order_by(desc(PageVisit.visit_time)).offset(offset).limit(limit).all()
    total = query.count()
    
    return {
        "visits": visits,
        "total": total,
        "limit": limit,
        "offset": offset
    }

@router.get("/admin/whatsapp-clicks")
async def get_whatsapp_clicks(
    current_admin: Admin = Depends(get_current_active_admin),
    limit: int = 100,
    offset: int = 0,
    days: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Get WhatsApp click analytics"""
    query = db.query(WhatsAppClick)
    
    if days:
        since_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(WhatsAppClick.click_time >= since_date)
    
    clicks = query.order_by(desc(WhatsAppClick.click_time)).offset(offset).limit(limit).all()
    total = query.count()
    
    return {
        "clicks": clicks,
        "total": total,
        "limit": limit,
        "offset": offset
    }

@router.get("/admin/donations")
async def get_donations(
    current_admin: Admin = Depends(get_current_active_admin),
    limit: int = 100,
    offset: int = 0,
    days: Optional[int] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get donation analytics"""
    query = db.query(DonationTracking)
    
    if days:
        since_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(DonationTracking.donation_time >= since_date)
    
    if status:
        query = query.filter(DonationTracking.payment_status == status)
    
    donations = query.order_by(desc(DonationTracking.donation_time)).offset(offset).limit(limit).all()
    total = query.count()
    
    # Calculate totals
    total_amount = db.query(func.sum(DonationTracking.amount)).filter(
        DonationTracking.payment_status == "completed"
    ).scalar() or 0
    
    if days:
        recent_amount = db.query(func.sum(DonationTracking.amount)).filter(
            and_(
                DonationTracking.payment_status == "completed",
                DonationTracking.completion_time >= datetime.utcnow() - timedelta(days=days)
            )
        ).scalar() or 0
    else:
        recent_amount = total_amount
    
    return {
        "donations": donations,
        "total": total,
        "total_amount": total_amount,
        "recent_amount": recent_amount,
        "limit": limit,
        "offset": offset
    }

@router.get("/admin/analytics-summary")
async def get_analytics_summary(
    current_admin: Admin = Depends(get_current_active_admin),
    days: int = 30,
    db: Session = Depends(get_db)
):
    """Get analytics summary for dashboard"""
    since_date = datetime.utcnow() - timedelta(days=days)
    
    # Page visits
    total_visits = db.query(PageVisit).filter(PageVisit.visit_time >= since_date).count()
    unique_visitors = db.query(func.count(func.distinct(PageVisit.ip_address))).filter(
        PageVisit.visit_time >= since_date
    ).scalar()
    
    # WhatsApp clicks
    total_whatsapp_clicks = db.query(WhatsAppClick).filter(WhatsAppClick.click_time >= since_date).count()
    unique_whatsapp_clickers = db.query(func.count(func.distinct(WhatsAppClick.ip_address))).filter(
        WhatsAppClick.click_time >= since_date
    ).scalar()
    
    # Donations
    total_donations = db.query(DonationTracking).filter(
        and_(
            DonationTracking.donation_time >= since_date,
            DonationTracking.payment_status == "completed"
        )
    ).count()
    
    total_donation_amount = db.query(func.sum(DonationTracking.amount)).filter(
        and_(
            DonationTracking.completion_time >= since_date,
            DonationTracking.payment_status == "completed"
        )
    ).scalar() or 0
    
    return {
        "period_days": days,
        "page_visits": {
            "total": total_visits,
            "unique_visitors": unique_visitors
        },
        "whatsapp_clicks": {
            "total": total_whatsapp_clicks,
            "unique_clickers": unique_whatsapp_clickers
        },
        "donations": {
            "total_count": total_donations,
            "total_amount": total_donation_amount
        }
    }
