from fastapi import API<PERSON>outer, Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Optional
import jwt
import os
from dotenv import load_dotenv

from app.models.database import get_db
from app.models.admin import Admin
from app.schemas.admin import AdminLogin, Token, TokenData

load_dotenv()

router = APIRouter()

# JWT Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 480  # 8 hours for better user experience

security = HTTPBearer()

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        token_data = TokenData(username=username)
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return token_data

def verify_token_alternative(request: Request):
    """Alternative token verification that reads from headers or cookies"""
    token = None

    try:
        # Try to get token from Authorization header first
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]

        # If no header token, try to get from cookie
        if not token:
            token = request.cookies.get("admin_token")

        # If still no token, authentication failed
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated - No token found in header or cookie",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verify the token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")

        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated - Invalid token payload",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return TokenData(username=username)

    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated - Token expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.PyJWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Not authenticated - JWT error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Not authenticated - Error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_admin(token_data: TokenData = Depends(verify_token), db: Session = Depends(get_db)):
    admin = db.query(Admin).filter(Admin.username == token_data.username).first()
    if admin is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not admin.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive admin"
        )
    return admin

def get_current_active_admin(current_admin: Admin = Depends(get_current_admin)):
    if not current_admin.is_active:
        raise HTTPException(status_code=400, detail="Inactive admin")
    return current_admin

@router.post("/login", response_model=Token)
async def login_for_access_token(admin_login: AdminLogin, db: Session = Depends(get_db)):
    admin = db.query(Admin).filter(Admin.username == admin_login.username).first()
    if not admin or not admin.verify_password(admin_login.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not admin.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Admin account is deactivated"
        )

    # Update last login
    admin.last_login = datetime.utcnow()
    db.commit()

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": admin.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/logout")
async def logout():
    return {"message": "Successfully logged out"}

@router.get("/me")
async def read_admin_me(current_admin: Admin = Depends(get_current_active_admin)):
    return current_admin

@router.get("/me-alt")
async def read_admin_me_alternative(request: Request, db: Session = Depends(get_db)):
    """Alternative /me endpoint using direct header parsing"""
    try:
        token_data = verify_token_alternative(request)
        admin = db.query(Admin).filter(Admin.username == token_data.username).first()
        if admin is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Admin not found",
                headers={"WWW-Authenticate": "Bearer"},
            )
        if not admin.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive admin"
            )
        return admin
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal error: {str(e)}"
        )

@router.get("/debug")
async def debug_auth(db: Session = Depends(get_db)):
    """Debug endpoint to check authentication setup"""
    try:
        # Count admins
        admin_count = db.query(Admin).count()
        active_admin_count = db.query(Admin).filter(Admin.is_active == True).count()

        # Get first admin for testing
        first_admin = db.query(Admin).first()

        return {
            "status": "ok",
            "admin_count": admin_count,
            "active_admin_count": active_admin_count,
            "first_admin_username": first_admin.username if first_admin else None,
            "secret_key_set": bool(SECRET_KEY and SECRET_KEY != "your-secret-key-change-this-in-production"),
            "secret_key_length": len(SECRET_KEY) if SECRET_KEY else 0,
            "secret_key_preview": SECRET_KEY[:10] + "..." if SECRET_KEY and len(SECRET_KEY) > 10 else SECRET_KEY,
            "token_expire_minutes": ACCESS_TOKEN_EXPIRE_MINUTES,
            "algorithm": ALGORITHM
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

@router.post("/debug-token")
async def debug_token(token_data: dict):
    """Debug endpoint to test token verification"""
    try:
        token = token_data.get("token")
        if not token:
            return {"status": "error", "error": "No token provided"}

        # Try to decode the token
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return {
                "status": "token_valid",
                "payload": payload,
                "secret_key_preview": SECRET_KEY[:10] + "..." if SECRET_KEY and len(SECRET_KEY) > 10 else SECRET_KEY,
                "algorithm": ALGORITHM
            }
        except jwt.ExpiredSignatureError:
            return {"status": "token_expired", "error": "Token has expired"}
        except jwt.InvalidTokenError as e:
            return {"status": "token_invalid", "error": str(e)}
        except Exception as e:
            return {"status": "decode_error", "error": str(e)}

    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

@router.get("/debug-headers")
async def debug_headers(request: Request):
    """Debug endpoint to check request headers"""
    try:
        headers = dict(request.headers)
        auth_header = headers.get("authorization", "Not found")

        return {
            "status": "ok",
            "authorization_header": auth_header,
            "all_headers": headers,
            "has_bearer": "Bearer" in auth_header if auth_header != "Not found" else False
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }