from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timezone
import uuid

from app.models.database import get_db
from app.models.group import CommunityGroup, NotificationFrequency
from app.models.report import ReportCategory
from app.schemas.group import GroupCreate, GroupResponse, GroupUpdate, GroupListResponse

router = APIRouter()

@router.post("/", response_model=GroupResponse)
def create_group(group: GroupCreate, db: Session = Depends(get_db)):
    """Create a new community group"""

    # Validate admin_phone format if provided
    if group.admin_phone:
        import re
        phone_pattern = r'^\+27[0-9]{9}$'
        if not re.match(phone_pattern, group.admin_phone):
            raise HTTPException(
                status_code=400,
                detail="Invalid admin phone number format. Use +27xxxxxxxxx"
            )

    # Create the group
    db_group = CommunityGroup(
        id=uuid.uuid4(),
        name=group.name,
        description=group.description,
        admin_contact=group.admin_contact,
        admin_phone=group.admin_phone,  # Save the admin phone number
        whatsapp_group_id=group.whatsapp_group_id,
        notification_frequency=group.notification_frequency,
        categories=group.categories,
        created_at=datetime.now(timezone.utc),
        active=True
    )

    # Set the area coordinates (this will update area_json and area_wkt)
    db_group.area_coordinates = group.area_coordinates

    # Add categories
    for category in group.categories:
        db_group.categories.append(category)

    db.add(db_group)
    db.commit()
    db.refresh(db_group)

    return db_group

@router.get("/", response_model=List[GroupListResponse])
def get_groups(
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get all community groups"""
    query = db.query(CommunityGroup)

    if active_only:
        query = query.filter(CommunityGroup.active == True)

    groups = query.all()

    # Format the response to match the GroupListResponse schema
    response = []
    for group in groups:
        # Get area coordinates and ensure they're in the correct format
        area_coordinates = group.area_coordinates

        # Ensure we have at least 3 points for a valid polygon
        if len(area_coordinates) < 3:
            print(f"Warning: Group {group.id} has fewer than 3 coordinates: {area_coordinates}")

        # Create the response item
        response_item = GroupListResponse(
            id=group.id,
            name=group.name,
            description=group.description,
            notification_frequency=group.notification_frequency,
            categories=group.categories,
            area_coordinates=area_coordinates
        )
        response.append(response_item)

    return response

@router.get("/{group_id}", response_model=GroupResponse)
def get_group(group_id: str, db: Session = Depends(get_db)):
    """Get a specific community group by ID"""
    group = db.query(CommunityGroup).filter(CommunityGroup.id == group_id).first()

    if not group:
        raise HTTPException(status_code=404, detail="Group not found")

    return group

@router.put("/{group_id}", response_model=GroupResponse)
def update_group(group_id: str, group_update: GroupUpdate, db: Session = Depends(get_db)):
    """Update a community group"""
    db_group = db.query(CommunityGroup).filter(CommunityGroup.id == group_id).first()

    if not db_group:
        raise HTTPException(status_code=404, detail="Group not found")

    # Update fields
    if group_update.name is not None:
        db_group.name = group_update.name

    if group_update.description is not None:
        db_group.description = group_update.description

    if group_update.admin_contact is not None:
        db_group.admin_contact = group_update.admin_contact

    if group_update.whatsapp_group_id is not None:
        db_group.whatsapp_group_id = group_update.whatsapp_group_id

    if group_update.notification_frequency is not None:
        db_group.notification_frequency = group_update.notification_frequency

    if group_update.active is not None:
        db_group.active = group_update.active

    if group_update.area_coordinates is not None:
        # Update the area coordinates
        db_group.area_coordinates = group_update.area_coordinates

    if group_update.categories is not None:
        # Clear existing categories
        db_group.categories = []

        # Add new categories
        for category in group_update.categories:
            db_group.categories.append(category)

    db.commit()
    db.refresh(db_group)

    return db_group

@router.delete("/{group_id}")
def delete_group(group_id: str, db: Session = Depends(get_db)):
    """Delete a community group (soft delete)"""
    db_group = db.query(CommunityGroup).filter(CommunityGroup.id == group_id).first()

    if not db_group:
        raise HTTPException(status_code=404, detail="Group not found")

    # Soft delete
    db_group.active = False
    db.commit()

    return {"message": "Group deactivated successfully"}

@router.post("/remove-by-phone")
def remove_groups_by_phone(
    phone_number: str = Body(..., embed=True),
    db: Session = Depends(get_db)
):
    """Remove all community groups associated with a phone number"""

    # Validate phone number format
    import re
    phone_pattern = r'^\+27[0-9]{9}$'
    if not re.match(phone_pattern, phone_number):
        raise HTTPException(
            status_code=400,
            detail="Invalid phone number format. Use +27xxxxxxxxx"
        )

    # Find groups with this phone number in admin_phone or admin_contact
    groups_to_remove = db.query(CommunityGroup).filter(
        (CommunityGroup.admin_phone == phone_number) |
        (CommunityGroup.admin_contact == phone_number)
    ).all()

    if not groups_to_remove:
        return {
            "message": "No groups found with this phone number",
            "removed_count": 0,
            "groups_removed": []
        }

    # Collect group names before deletion
    group_names = [group.name for group in groups_to_remove]

    # Delete the groups (hard delete for privacy compliance)
    for group in groups_to_remove:
        db.delete(group)

    db.commit()

    return {
        "message": f"Successfully removed {len(groups_to_remove)} group(s)",
        "removed_count": len(groups_to_remove),
        "groups_removed": group_names
    }
