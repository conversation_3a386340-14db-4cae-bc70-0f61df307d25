"""
Subscription API for Hello Cyril API access
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request, Header, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import json
import logging
import asyncio

from app.models.database import get_db
from app.models.subscription import (
    Subscriber, APIKey, APIUsageLog, SubscriptionPayment, AlertSubscription,
    SubscriptionTier, SubscriptionStatus, APIKeyStatus
)
from app.schemas.subscription import (
    SubscriberCreate, SubscriberResponse, APIKeyCreate, APIKeyResponse,
    SubscriptionUpdate, UsageStatsResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)


# Dependency to verify API key
async def verify_api_key(
    request: Request,
    x_api_key: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> tuple[Subscriber, <PERSON>K<PERSON>]:
    """Verify API key and return subscriber and key"""

    if not x_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required in X-API-Key header"
        )

    # Hash the provided key
    key_hash = APIKey.hash_key(x_api_key)

    # Find the API key
    api_key = db.query(APIKey).filter(
        APIKey.key_hash == key_hash,
        APIKey.status == APIKeyStatus.ACTIVE
    ).first()

    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )

    # Check if key is expired
    if not api_key.is_valid():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key expired or inactive"
        )

    # Get subscriber
    subscriber = api_key.subscriber

    # Check if subscriber can make requests
    if not subscriber.can_make_request():
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Monthly request limit exceeded or subscription inactive"
        )

    # Log the usage
    await log_api_usage(request, subscriber, api_key, db)

    # Increment usage counters
    subscriber.increment_usage()
    api_key.increment_usage()
    db.commit()

    return subscriber, api_key


async def log_api_usage(
    request: Request,
    subscriber: Subscriber,
    api_key: APIKey,
    db: Session
):
    """Log API usage for billing and analytics"""
    try:
        usage_log = APIUsageLog(
            endpoint=str(request.url.path),
            method=request.method,
            ip_address=request.client.host if request.client else "unknown",
            user_agent=request.headers.get("user-agent"),
            status_code=200,  # Will be updated later if needed
            subscriber_id=subscriber.id,
            api_key_id=api_key.id
        )

        db.add(usage_log)
        db.commit()

    except Exception as e:
        logger.error(f"Failed to log API usage: {str(e)}")


# Public subscription management endpoints
@router.post("/subscribe", response_model=SubscriberResponse)
async def create_subscription(
    subscriber_data: SubscriberCreate,
    db: Session = Depends(get_db)
):
    """Create a new subscription"""

    # Check if email already exists
    existing = db.query(Subscriber).filter(Subscriber.email == subscriber_data.email).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Get tier configuration
    tier_config = Subscriber().get_tier_limits()
    if hasattr(subscriber_data, 'subscription_tier'):
        temp_subscriber = Subscriber(subscription_tier=subscriber_data.subscription_tier)
        tier_config = temp_subscriber.get_tier_limits()

    # Create subscriber
    subscriber = Subscriber(
        email=subscriber_data.email,
        company_name=subscriber_data.company_name,
        contact_name=subscriber_data.contact_name,
        phone_number=subscriber_data.phone_number,
        subscription_tier=getattr(subscriber_data, 'subscription_tier', SubscriptionTier.BASIC),
        monthly_fee=tier_config['monthly_fee'],
        monthly_request_limit=tier_config['monthly_requests'],
        subscription_status=SubscriptionStatus.PENDING
    )

    db.add(subscriber)
    db.commit()
    db.refresh(subscriber)

    return subscriber


@router.post("/subscribers/{subscriber_id}/api-keys", response_model=APIKeyResponse)
async def create_api_key(
    subscriber_id: str,
    key_data: APIKeyCreate,
    db: Session = Depends(get_db)
):
    """Create a new API key for a subscriber"""

    # Get subscriber
    subscriber = db.query(Subscriber).filter(Subscriber.id == subscriber_id).first()
    if not subscriber:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscriber not found"
        )

    # Check if subscriber is active
    if not subscriber.is_active():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Subscription must be active to create API keys"
        )

    # Generate API key
    key, key_hash, key_prefix = APIKey.generate_api_key()

    # Create API key record
    api_key = APIKey(
        key_name=key_data.key_name,
        key_hash=key_hash,
        key_prefix=key_prefix,
        subscriber_id=subscriber.id,
        permissions=json.dumps(key_data.permissions) if key_data.permissions else None,
        expires_at=key_data.expires_at
    )

    db.add(api_key)
    db.commit()
    db.refresh(api_key)

    # Return the actual key (only time it's shown)
    response = APIKeyResponse(
        id=api_key.id,
        key_name=api_key.key_name,
        key_prefix=api_key.key_prefix,
        status=api_key.status,
        created_at=api_key.created_at,
        expires_at=api_key.expires_at,
        api_key=key  # Only returned on creation
    )

    return response


@router.get("/subscribers/{subscriber_id}/usage", response_model=UsageStatsResponse)
async def get_usage_stats(
    subscriber_id: str,
    days: int = 30,
    db: Session = Depends(get_db)
):
    """Get usage statistics for a subscriber"""

    # Get subscriber
    subscriber = db.query(Subscriber).filter(Subscriber.id == subscriber_id).first()
    if not subscriber:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscriber not found"
        )

    # Get usage data
    cutoff_date = datetime.utcnow() - timedelta(days=days)

    usage_logs = db.query(APIUsageLog).filter(
        APIUsageLog.subscriber_id == subscriber.id,
        APIUsageLog.timestamp >= cutoff_date
    ).all()

    # Calculate statistics
    total_requests = len(usage_logs)
    successful_requests = len([log for log in usage_logs if log.status_code < 400])
    failed_requests = total_requests - successful_requests

    # Group by endpoint
    endpoint_stats = {}
    for log in usage_logs:
        if log.endpoint not in endpoint_stats:
            endpoint_stats[log.endpoint] = 0
        endpoint_stats[log.endpoint] += 1

    # Group by day
    daily_stats = {}
    for log in usage_logs:
        day = log.timestamp.date().isoformat()
        if day not in daily_stats:
            daily_stats[day] = 0
        daily_stats[day] += 1

    return UsageStatsResponse(
        subscriber_id=subscriber.id,
        period_days=days,
        total_requests=total_requests,
        successful_requests=successful_requests,
        failed_requests=failed_requests,
        current_month_usage=subscriber.current_month_requests,
        monthly_limit=subscriber.monthly_request_limit,
        usage_percentage=(subscriber.current_month_requests / subscriber.monthly_request_limit) * 100,
        endpoint_breakdown=endpoint_stats,
        daily_breakdown=daily_stats,
        subscription_tier=subscriber.subscription_tier.value,
        subscription_status=subscriber.subscription_status.value
    )


# Protected API endpoints (require API key)
@router.get("/reports", dependencies=[Depends(verify_api_key)])
async def get_reports_api(
    request: Request,
    category: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get reports via API (requires subscription)"""

    # Import here to avoid circular imports
    from app.models.report import Report, ReportCategory
    from sqlalchemy import and_

    # Build query
    query = db.query(Report)

    # Apply filters
    if category:
        try:
            category_enum = ReportCategory(category)
            query = query.filter(Report.category == category_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category: {category}"
            )

    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date)
            query = query.filter(Report.timestamp >= start_dt)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid start_date format. Use ISO format."
            )

    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date)
            query = query.filter(Report.timestamp <= end_dt)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid end_date format. Use ISO format."
            )

    # Apply pagination
    query = query.offset(offset).limit(min(limit, 1000))  # Max 1000 per request

    reports = query.all()

    # Format response
    return {
        "reports": [
            {
                "id": str(report.id),
                "category": report.category.value,
                "description": report.description,
                "location": {
                    "latitude": report.latitude,
                    "longitude": report.longitude
                },
                "timestamp": report.timestamp.isoformat(),
                "verified": report.verified
            }
            for report in reports
        ],
        "total": len(reports),
        "offset": offset,
        "limit": limit
    }


@router.get("/stats", dependencies=[Depends(verify_api_key)])
async def get_stats_api(
    request: Request,
    period: str = "overall",
    db: Session = Depends(get_db)
):
    """Get platform statistics via API (requires subscription)"""

    # Import here to avoid circular imports
    from app.services.stats_service import StatsService

    try:
        stats_service = StatsService(db)
        stats = stats_service.get_all_stats(period=period)

        return {
            "stats": stats,
            "period": period,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve statistics"
        )


# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, api_key: str):
        await websocket.accept()
        self.active_connections[api_key] = websocket

    def disconnect(self, api_key: str):
        if api_key in self.active_connections:
            del self.active_connections[api_key]

    async def send_personal_message(self, message: str, api_key: str):
        if api_key in self.active_connections:
            try:
                await self.active_connections[api_key].send_text(message)
            except:
                # Connection closed, remove it
                self.disconnect(api_key)

    async def broadcast(self, message: str):
        disconnected = []
        for api_key, connection in self.active_connections.items():
            try:
                await connection.send_text(message)
            except:
                disconnected.append(api_key)

        # Clean up disconnected connections
        for api_key in disconnected:
            self.disconnect(api_key)

manager = ConnectionManager()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, api_key: str, db: Session = Depends(get_db)):
    """WebSocket endpoint for real-time alerts (requires API key)"""

    # Verify API key
    try:
        api_key_record = db.query(APIKey).filter(
            APIKey.api_key_hash == api_key,
            APIKey.status == APIKeyStatus.ACTIVE
        ).first()

        if not api_key_record or not api_key_record.is_valid():
            await websocket.close(code=4001, reason="Invalid API key")
            return

        subscriber = api_key_record.subscriber
        if not subscriber.can_make_request():
            await websocket.close(code=4002, reason="Subscription inactive or limit exceeded")
            return

    except Exception as e:
        await websocket.close(code=4000, reason="Authentication failed")
        return

    # Connect to WebSocket
    await manager.connect(websocket, api_key)

    try:
        # Send welcome message
        welcome_msg = {
            "type": "connection",
            "message": "Connected to Hello Cyril real-time alerts",
            "subscription_tier": subscriber.subscription_tier.value,
            "timestamp": datetime.utcnow().isoformat()
        }
        await websocket.send_text(json.dumps(welcome_msg))

        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (ping/pong, subscription updates)
                data = await websocket.receive_text()
                message = json.loads(data)

                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong", "timestamp": datetime.utcnow().isoformat()}))

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket error: {str(e)}")
                break

    except WebSocketDisconnect:
        pass
    finally:
        manager.disconnect(api_key)


@router.post("/webhooks/subscribe")
async def subscribe_to_webhooks(
    webhook_data: Dict[str, Any],
    subscriber: Subscriber = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Subscribe to webhook notifications (Premium/Enterprise only)"""

    # Check subscription tier
    if subscriber.subscription_tier == SubscriptionTier.BASIC:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Webhooks are only available for Premium and Enterprise subscribers"
        )

    webhook_url = webhook_data.get("webhook_url")
    alert_types = webhook_data.get("alert_types", ["all"])
    geographic_filter = webhook_data.get("geographic_filter")

    if not webhook_url:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="webhook_url is required"
        )

    # Validate webhook URL
    if not webhook_url.startswith(("http://", "https://")):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="webhook_url must be a valid HTTP/HTTPS URL"
        )

    # Create or update webhook subscription
    existing_webhook = db.query(AlertSubscription).filter(
        AlertSubscription.subscriber_id == subscriber.id
    ).first()

    if existing_webhook:
        existing_webhook.webhook_url = webhook_url
        existing_webhook.alert_types = json.dumps(alert_types)
        existing_webhook.geographic_filter = json.dumps(geographic_filter) if geographic_filter else None
        existing_webhook.is_active = True
        existing_webhook.failed_deliveries = 0
    else:
        webhook_subscription = AlertSubscription(
            subscriber_id=subscriber.id,
            webhook_url=webhook_url,
            alert_types=json.dumps(alert_types),
            geographic_filter=json.dumps(geographic_filter) if geographic_filter else None,
            is_active=True
        )
        db.add(webhook_subscription)

    db.commit()

    return {
        "message": "Webhook subscription created successfully",
        "webhook_url": webhook_url,
        "alert_types": alert_types,
        "geographic_filter": geographic_filter
    }


@router.get("/webhooks/status")
async def get_webhook_status(
    subscriber: Subscriber = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Get webhook subscription status"""

    webhook = db.query(AlertSubscription).filter(
        AlertSubscription.subscriber_id == subscriber.id
    ).first()

    if not webhook:
        return {
            "subscribed": False,
            "message": "No webhook subscription found"
        }

    return {
        "subscribed": True,
        "webhook_url": webhook.webhook_url,
        "alert_types": json.loads(webhook.alert_types) if webhook.alert_types else [],
        "geographic_filter": json.loads(webhook.geographic_filter) if webhook.geographic_filter else None,
        "is_active": webhook.is_active,
        "failed_deliveries": webhook.failed_deliveries,
        "last_delivery_attempt": webhook.last_delivery_attempt.isoformat() if webhook.last_delivery_attempt else None
    }


@router.delete("/webhooks/unsubscribe")
async def unsubscribe_from_webhooks(
    subscriber: Subscriber = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Unsubscribe from webhook notifications"""

    webhook = db.query(AlertSubscription).filter(
        AlertSubscription.subscriber_id == subscriber.id
    ).first()

    if not webhook:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No webhook subscription found"
        )

    webhook.is_active = False
    db.commit()

    return {"message": "Successfully unsubscribed from webhooks"}


# Function to send real-time alerts to WebSocket connections
async def broadcast_alert(alert_data: Dict[str, Any]):
    """Broadcast alert to all connected WebSocket clients"""
    message = json.dumps({
        "type": "alert",
        "data": alert_data,
        "timestamp": datetime.utcnow().isoformat()
    })

    await manager.broadcast(message)


@router.post("/webhooks/test")
async def test_webhook(
    subscriber: Subscriber = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Test webhook delivery (Premium/Enterprise only)"""

    # Check subscription tier
    if subscriber.subscription_tier == SubscriptionTier.BASIC:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Webhook testing is only available for Premium and Enterprise subscribers"
        )

    # Get webhook subscription
    webhook = db.query(AlertSubscription).filter(
        AlertSubscription.subscriber_id == subscriber.id,
        AlertSubscription.is_active == True
    ).first()

    if not webhook:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active webhook subscription found. Please subscribe to webhooks first."
        )

    # Create test payload
    test_payload = {
        "event_type": "test_webhook",
        "timestamp": datetime.utcnow().isoformat(),
        "data": {
            "message": "This is a test webhook from Hello Cyril API",
            "test": True,
            "subscriber_id": subscriber.id,
            "subscription_tier": subscriber.subscription_tier.value
        }
    }

    # Send test webhook
    import aiohttp
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                webhook.webhook_url,
                json=test_payload,
                headers={"Content-Type": "application/json"},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:

                if response.status == 200:
                    return {
                        "message": "Test webhook sent successfully",
                        "webhook_url": webhook.webhook_url,
                        "status_code": response.status,
                        "test_payload": test_payload
                    }
                else:
                    return {
                        "message": "Test webhook sent but received non-200 response",
                        "webhook_url": webhook.webhook_url,
                        "status_code": response.status,
                        "response_text": await response.text()
                    }

    except asyncio.TimeoutError:
        raise HTTPException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail="Webhook test timed out. Please check your webhook URL."
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send test webhook: {str(e)}"
        )


@router.get("/usage")
async def get_api_usage(
    subscriber: Subscriber = Depends(verify_api_key),
    db: Session = Depends(get_db)
):
    """Get current API usage statistics"""

    # Calculate usage percentage
    usage_percentage = (subscriber.current_month_requests / subscriber.monthly_request_limit) * 100

    # Get recent usage logs
    recent_logs = db.query(APIUsageLog).filter(
        APIUsageLog.subscriber_id == subscriber.id
    ).order_by(APIUsageLog.timestamp.desc()).limit(10).all()

    return {
        "subscriber_id": subscriber.id,
        "subscription_tier": subscriber.subscription_tier.value,
        "subscription_status": subscriber.subscription_status.value,
        "current_month_requests": subscriber.current_month_requests,
        "monthly_request_limit": subscriber.monthly_request_limit,
        "usage_percentage": round(usage_percentage, 2),
        "requests_remaining": subscriber.monthly_request_limit - subscriber.current_month_requests,
        "billing_cycle_start": subscriber.billing_cycle_start.isoformat() if subscriber.billing_cycle_start else None,
        "billing_cycle_end": subscriber.billing_cycle_end.isoformat() if subscriber.billing_cycle_end else None,
        "recent_requests": [
            {
                "endpoint": log.endpoint,
                "method": log.method,
                "timestamp": log.timestamp.isoformat(),
                "status_code": log.status_code
            }
            for log in recent_logs
        ]
    }
