"""
Subscription API for Hello Cyril API access
"""

from fastapi import APIR<PERSON><PERSON>, Depends, HTTPException, status, Request, Header
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import json
import logging

from app.models.database import get_db
from app.models.subscription import (
    Subscriber, APIKey, APIUsageLog, SubscriptionPayment, AlertSubscription,
    SubscriptionTier, SubscriptionStatus, APIKeyStatus
)
from app.schemas.subscription import (
    SubscriberCreate, SubscriberResponse, APIKeyCreate, APIKeyResponse,
    SubscriptionUpdate, UsageStatsResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)


# Dependency to verify API key
async def verify_api_key(
    request: Request,
    x_api_key: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> tuple[Subscriber, APIKey]:
    """Verify API key and return subscriber and key"""
    
    if not x_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required in X-API-Key header"
        )
    
    # Hash the provided key
    key_hash = APIKey.hash_key(x_api_key)
    
    # Find the API key
    api_key = db.query(APIKey).filter(
        APIKey.key_hash == key_hash,
        APIKey.status == APIKeyStatus.ACTIVE
    ).first()
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    
    # Check if key is expired
    if not api_key.is_valid():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key expired or inactive"
        )
    
    # Get subscriber
    subscriber = api_key.subscriber
    
    # Check if subscriber can make requests
    if not subscriber.can_make_request():
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Monthly request limit exceeded or subscription inactive"
        )
    
    # Log the usage
    await log_api_usage(request, subscriber, api_key, db)
    
    # Increment usage counters
    subscriber.increment_usage()
    api_key.increment_usage()
    db.commit()
    
    return subscriber, api_key


async def log_api_usage(
    request: Request,
    subscriber: Subscriber,
    api_key: APIKey,
    db: Session
):
    """Log API usage for billing and analytics"""
    try:
        usage_log = APIUsageLog(
            endpoint=str(request.url.path),
            method=request.method,
            ip_address=request.client.host if request.client else "unknown",
            user_agent=request.headers.get("user-agent"),
            status_code=200,  # Will be updated later if needed
            subscriber_id=subscriber.id,
            api_key_id=api_key.id
        )
        
        db.add(usage_log)
        db.commit()
        
    except Exception as e:
        logger.error(f"Failed to log API usage: {str(e)}")


# Public subscription management endpoints
@router.post("/subscribe", response_model=SubscriberResponse)
async def create_subscription(
    subscriber_data: SubscriberCreate,
    db: Session = Depends(get_db)
):
    """Create a new subscription"""
    
    # Check if email already exists
    existing = db.query(Subscriber).filter(Subscriber.email == subscriber_data.email).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Get tier configuration
    tier_config = Subscriber().get_tier_limits()
    if hasattr(subscriber_data, 'subscription_tier'):
        temp_subscriber = Subscriber(subscription_tier=subscriber_data.subscription_tier)
        tier_config = temp_subscriber.get_tier_limits()
    
    # Create subscriber
    subscriber = Subscriber(
        email=subscriber_data.email,
        company_name=subscriber_data.company_name,
        contact_name=subscriber_data.contact_name,
        phone_number=subscriber_data.phone_number,
        subscription_tier=getattr(subscriber_data, 'subscription_tier', SubscriptionTier.BASIC),
        monthly_fee=tier_config['monthly_fee'],
        monthly_request_limit=tier_config['monthly_requests'],
        subscription_status=SubscriptionStatus.PENDING
    )
    
    db.add(subscriber)
    db.commit()
    db.refresh(subscriber)
    
    return subscriber


@router.post("/subscribers/{subscriber_id}/api-keys", response_model=APIKeyResponse)
async def create_api_key(
    subscriber_id: str,
    key_data: APIKeyCreate,
    db: Session = Depends(get_db)
):
    """Create a new API key for a subscriber"""
    
    # Get subscriber
    subscriber = db.query(Subscriber).filter(Subscriber.id == subscriber_id).first()
    if not subscriber:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscriber not found"
        )
    
    # Check if subscriber is active
    if not subscriber.is_active():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Subscription must be active to create API keys"
        )
    
    # Generate API key
    key, key_hash, key_prefix = APIKey.generate_api_key()
    
    # Create API key record
    api_key = APIKey(
        key_name=key_data.key_name,
        key_hash=key_hash,
        key_prefix=key_prefix,
        subscriber_id=subscriber.id,
        permissions=json.dumps(key_data.permissions) if key_data.permissions else None,
        expires_at=key_data.expires_at
    )
    
    db.add(api_key)
    db.commit()
    db.refresh(api_key)
    
    # Return the actual key (only time it's shown)
    response = APIKeyResponse(
        id=api_key.id,
        key_name=api_key.key_name,
        key_prefix=api_key.key_prefix,
        status=api_key.status,
        created_at=api_key.created_at,
        expires_at=api_key.expires_at,
        api_key=key  # Only returned on creation
    )
    
    return response


@router.get("/subscribers/{subscriber_id}/usage", response_model=UsageStatsResponse)
async def get_usage_stats(
    subscriber_id: str,
    days: int = 30,
    db: Session = Depends(get_db)
):
    """Get usage statistics for a subscriber"""
    
    # Get subscriber
    subscriber = db.query(Subscriber).filter(Subscriber.id == subscriber_id).first()
    if not subscriber:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscriber not found"
        )
    
    # Get usage data
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    
    usage_logs = db.query(APIUsageLog).filter(
        APIUsageLog.subscriber_id == subscriber.id,
        APIUsageLog.timestamp >= cutoff_date
    ).all()
    
    # Calculate statistics
    total_requests = len(usage_logs)
    successful_requests = len([log for log in usage_logs if log.status_code < 400])
    failed_requests = total_requests - successful_requests
    
    # Group by endpoint
    endpoint_stats = {}
    for log in usage_logs:
        if log.endpoint not in endpoint_stats:
            endpoint_stats[log.endpoint] = 0
        endpoint_stats[log.endpoint] += 1
    
    # Group by day
    daily_stats = {}
    for log in usage_logs:
        day = log.timestamp.date().isoformat()
        if day not in daily_stats:
            daily_stats[day] = 0
        daily_stats[day] += 1
    
    return UsageStatsResponse(
        subscriber_id=subscriber.id,
        period_days=days,
        total_requests=total_requests,
        successful_requests=successful_requests,
        failed_requests=failed_requests,
        current_month_usage=subscriber.current_month_requests,
        monthly_limit=subscriber.monthly_request_limit,
        usage_percentage=(subscriber.current_month_requests / subscriber.monthly_request_limit) * 100,
        endpoint_breakdown=endpoint_stats,
        daily_breakdown=daily_stats,
        subscription_tier=subscriber.subscription_tier.value,
        subscription_status=subscriber.subscription_status.value
    )


# Protected API endpoints (require API key)
@router.get("/reports", dependencies=[Depends(verify_api_key)])
async def get_reports_api(
    request: Request,
    category: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get reports via API (requires subscription)"""
    
    # Import here to avoid circular imports
    from app.models.report import Report, ReportCategory
    from sqlalchemy import and_
    
    # Build query
    query = db.query(Report)
    
    # Apply filters
    if category:
        try:
            category_enum = ReportCategory(category)
            query = query.filter(Report.category == category_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category: {category}"
            )
    
    if start_date:
        try:
            start_dt = datetime.fromisoformat(start_date)
            query = query.filter(Report.timestamp >= start_dt)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid start_date format. Use ISO format."
            )
    
    if end_date:
        try:
            end_dt = datetime.fromisoformat(end_date)
            query = query.filter(Report.timestamp <= end_dt)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid end_date format. Use ISO format."
            )
    
    # Apply pagination
    query = query.offset(offset).limit(min(limit, 1000))  # Max 1000 per request
    
    reports = query.all()
    
    # Format response
    return {
        "reports": [
            {
                "id": str(report.id),
                "category": report.category.value,
                "description": report.description,
                "location": {
                    "latitude": report.latitude,
                    "longitude": report.longitude
                },
                "timestamp": report.timestamp.isoformat(),
                "verified": report.verified
            }
            for report in reports
        ],
        "total": len(reports),
        "offset": offset,
        "limit": limit
    }


@router.get("/stats", dependencies=[Depends(verify_api_key)])
async def get_stats_api(
    request: Request,
    period: str = "overall",
    db: Session = Depends(get_db)
):
    """Get platform statistics via API (requires subscription)"""
    
    # Import here to avoid circular imports
    from app.services.stats_service import StatsService
    
    try:
        stats_service = StatsService(db)
        stats = stats_service.get_all_stats(period=period)
        
        return {
            "stats": stats,
            "period": period,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve statistics"
        )
