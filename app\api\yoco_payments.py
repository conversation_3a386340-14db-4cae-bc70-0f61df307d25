\
import httpx
import os
import uuid
from fastapi import APIRouter, HTTPException, Body, Request
from fastapi.responses import RedirectResponse # Added RedirectResponse
from pydantic import BaseModel, Field
from typing import Dict

router = APIRouter()

YOCO_SECRET_KEY = os.getenv("YOCO_SECRET_KEY")
YOCO_API_URL = "https://payments.yoco.com/api/checkouts"
APP_BASE_URL = os.getenv("APP_BASE_URL", "https://hellocyril.co.za")

class YocoDonationRequest(BaseModel):
    amount: float = Field(..., gt=0, description="Donation amount in Rands")

class YocoCheckoutResponse(BaseModel):
    redirectUrl: str
    yocoCheckoutId: str | None = None

@router.post("/donate/yoco/create-checkout", response_model=YocoCheckoutResponse)
async def create_yoco_checkout(donation_request: YocoDonationRequest):
    if not YOCO_SECRET_KEY:
        raise HTTPException(status_code=500, detail="Yoco secret key not configured. Please set the YOCO_SECRET_KEY environment variable.")

    amount_in_cents = int(donation_request.amount * 100)
    if amount_in_cents < 200:  # Yoco minimum R2.00
        raise HTTPException(status_code=400, detail="Donation amount must be at least R2.00")

    internal_transaction_id = str(uuid.uuid4())

    # Corrected callback URLs to include /api/payments prefix
    payload = {
        "amount": amount_in_cents,
        "currency": "ZAR",
        "successUrl": f"{APP_BASE_URL}/api/payments/payment/yoco/callback/success?transaction_id={internal_transaction_id}",
        "cancelUrl": f"{APP_BASE_URL}/api/payments/payment/yoco/callback/cancel?transaction_id={internal_transaction_id}",
        "failureUrl": f"{APP_BASE_URL}/api/payments/payment/yoco/callback/failure?transaction_id={internal_transaction_id}",
        "metadata": {
            "internal_transaction_id": internal_transaction_id,
            "description": f"Hello Cyril Donation - R{donation_request.amount:.2f}",
            "source": "HelloCyrilStatsPageDonation"
        }
    }

    headers = {
        "Authorization": f"Bearer {YOCO_SECRET_KEY}",
        "Content-Type": "application/json",
        "Idempotency-Key": str(uuid.uuid4())  # New key for each new checkout creation attempt
    }

    async with httpx.AsyncClient() as client:
        try:
            yoco_response = await client.post(YOCO_API_URL, json=payload, headers=headers)
            yoco_response.raise_for_status()  # Raises an exception for 4XX/5XX responses
            yoco_checkout_data = yoco_response.json()

            if "redirectUrl" not in yoco_checkout_data:
                # Log this error on the server for debugging
                print(f"Yoco API Error: 'redirectUrl' not in response. Data: {yoco_checkout_data}")
                raise HTTPException(status_code=500, detail="Yoco API did not return a redirectUrl. Please try again later.")

            return YocoCheckoutResponse(
                redirectUrl=yoco_checkout_data["redirectUrl"],
                yocoCheckoutId=yoco_checkout_data.get("id")
            )

        except httpx.HTTPStatusError as e:
            error_detail = f"Error from Yoco API: {e.response.status_code}"
            try:
                error_body = e.response.json()
                error_message = error_body.get('message', error_body.get('detail', str(error_body)))
                error_detail += f" - {error_message}"
                # Log this error on the server for debugging
                print(f"Yoco HTTPStatusError: {error_detail}, Response: {e.response.text}")
            except Exception:
                error_detail += f" - {e.response.text}"
                print(f"Yoco HTTPStatusError: {error_detail}, Raw Response: {e.response.text}")
            raise HTTPException(status_code=500, detail="Failed to communicate with Yoco. Please try again later.")
        except httpx.RequestError as e:
            # Log this error on the server for debugging
            print(f"Yoco RequestError: {str(e)}")
            raise HTTPException(status_code=503, detail="Could not connect to Yoco payment service. Please try again later.")
        except Exception as e:
            # Log this error on the server for debugging
            print(f"Unexpected error during Yoco checkout: {str(e)}")
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred. Please try again later.")

# Placeholder callback endpoints.
# IMPORTANT: Yoco recommends using webhooks to verify payment status reliably,
# not just relying on the user being redirected to the successUrl.
# These endpoints would typically show a message to the user and could trigger
# further backend processing if not solely relying on webhooks.

@router.get("/payment/yoco/callback/success")
async def yoco_payment_success(request: Request, transaction_id: str):
    # Track successful donation
    try:
        from app.api.analytics import track_donation
        from app.models.database import get_db

        # Get database session
        db = next(get_db())

        # Track the successful donation
        donation_data = {
            "amount": 0.0,  # Amount will be updated via webhook
            "payment_method": "yoco",
            "payment_status": "completed",
            "transaction_id": transaction_id,
            "payment_provider_id": transaction_id
        }

        await track_donation(request, donation_data, db)

    except Exception as e:
        # Don't fail the redirect if tracking fails
        print(f"Failed to track donation success: {str(e)}")

    # Redirect to the /stats page with query parameters
    stats_page_url = f"{APP_BASE_URL}/stats?payment_status=success&transaction_id={transaction_id}"
    return RedirectResponse(url=stats_page_url)

@router.get("/payment/yoco/callback/cancel")
async def yoco_payment_cancel(request: Request, transaction_id: str):
    # Log this event, update transaction status.
    # Display a cancellation message or redirect
    # For consistency, redirecting to stats page with a status
    stats_page_url = f"{APP_BASE_URL}/stats?payment_status=cancelled&transaction_id={transaction_id}"
    return RedirectResponse(url=stats_page_url)

@router.get("/payment/yoco/callback/failure")
async def yoco_payment_failure(request: Request, transaction_id: str):
    # Log this event, update transaction status.
    # Display a failure message or redirect
    # For consistency, redirecting to stats page with a status
    stats_page_url = f"{APP_BASE_URL}/stats?payment_status=failure&transaction_id={transaction_id}"
    return RedirectResponse(url=stats_page_url)

