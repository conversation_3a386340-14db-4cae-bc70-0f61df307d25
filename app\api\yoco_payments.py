\
import httpx
import os
import uuid
import secrets
import string
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Body, Request, Depends
from fastapi.responses import RedirectResponse, HTMLResponse
from pydantic import BaseModel, Field, EmailStr
from typing import Dict, Optional
from sqlalchemy.orm import Session

from app.models.database import get_db
from app.models.subscription import Subscriber, APIKey, SubscriptionTier, SubscriptionStatus, APIKeyStatus

router = APIRouter()

YOCO_SECRET_KEY = os.getenv("YOCO_SECRET_KEY")
YOCO_API_URL = "https://payments.yoco.com/api/checkouts"
APP_BASE_URL = os.getenv("APP_BASE_URL", "https://hellocyril.co.za")

class YocoDonationRequest(BaseModel):
    amount: float = Field(..., gt=0, description="Donation amount in Rands")

class YocoCheckoutResponse(BaseModel):
    redirectUrl: str
    yocoCheckoutId: str | None = None

class SubscriptionPaymentRequest(BaseModel):
    email: EmailStr
    contact_name: str
    company_name: Optional[str] = None
    phone_number: Optional[str] = None
    subscription_tier: str = Field(..., description="basic, premium, or enterprise")

class APIKeyResponse(BaseModel):
    api_key: str
    subscriber_id: str
    subscription_tier: str
    expires_at: str
    monthly_limit: int

class APIKeyRetrievalRequest(BaseModel):
    email: EmailStr
    contact_name: str

# Subscription pricing
SUBSCRIPTION_PRICING = {
    "basic": {"amount": 99.0, "requests": 10000, "name": "Basic Plan"},
    "premium": {"amount": 299.0, "requests": 50000, "name": "Premium Plan"},
    "enterprise": {"amount": 999.0, "requests": 200000, "name": "Enterprise Plan"}
}

def generate_api_key() -> str:
    """Generate a secure API key with Hello Cyril prefix"""
    random_part = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
    return f"hc_{random_part}"

def get_subscription_tier_enum(tier_str: str) -> SubscriptionTier:
    """Convert string to SubscriptionTier enum"""
    tier_map = {
        "basic": SubscriptionTier.BASIC,
        "premium": SubscriptionTier.PREMIUM,
        "enterprise": SubscriptionTier.ENTERPRISE
    }
    return tier_map.get(tier_str.lower(), SubscriptionTier.BASIC)

@router.post("/donate/yoco/create-checkout", response_model=YocoCheckoutResponse)
async def create_yoco_checkout(donation_request: YocoDonationRequest):
    if not YOCO_SECRET_KEY:
        raise HTTPException(status_code=500, detail="Yoco secret key not configured. Please set the YOCO_SECRET_KEY environment variable.")

    amount_in_cents = int(donation_request.amount * 100)
    if amount_in_cents < 200:  # Yoco minimum R2.00
        raise HTTPException(status_code=400, detail="Donation amount must be at least R2.00")

    internal_transaction_id = str(uuid.uuid4())

    # Corrected callback URLs to include /api/payments prefix
    payload = {
        "amount": amount_in_cents,
        "currency": "ZAR",
        "successUrl": f"{APP_BASE_URL}/api/payments/payment/yoco/callback/success?transaction_id={internal_transaction_id}",
        "cancelUrl": f"{APP_BASE_URL}/api/payments/payment/yoco/callback/cancel?transaction_id={internal_transaction_id}",
        "failureUrl": f"{APP_BASE_URL}/api/payments/payment/yoco/callback/failure?transaction_id={internal_transaction_id}",
        "metadata": {
            "internal_transaction_id": internal_transaction_id,
            "description": f"Hello Cyril Donation - R{donation_request.amount:.2f}",
            "source": "HelloCyrilStatsPageDonation"
        }
    }

    headers = {
        "Authorization": f"Bearer {YOCO_SECRET_KEY}",
        "Content-Type": "application/json",
        "Idempotency-Key": str(uuid.uuid4())  # New key for each new checkout creation attempt
    }

    async with httpx.AsyncClient() as client:
        try:
            yoco_response = await client.post(YOCO_API_URL, json=payload, headers=headers)
            yoco_response.raise_for_status()  # Raises an exception for 4XX/5XX responses
            yoco_checkout_data = yoco_response.json()

            if "redirectUrl" not in yoco_checkout_data:
                # Log this error on the server for debugging
                print(f"Yoco API Error: 'redirectUrl' not in response. Data: {yoco_checkout_data}")
                raise HTTPException(status_code=500, detail="Yoco API did not return a redirectUrl. Please try again later.")

            return YocoCheckoutResponse(
                redirectUrl=yoco_checkout_data["redirectUrl"],
                yocoCheckoutId=yoco_checkout_data.get("id")
            )

        except httpx.HTTPStatusError as e:
            error_detail = f"Error from Yoco API: {e.response.status_code}"
            try:
                error_body = e.response.json()
                error_message = error_body.get('message', error_body.get('detail', str(error_body)))
                error_detail += f" - {error_message}"
                # Log this error on the server for debugging
                print(f"Yoco HTTPStatusError: {error_detail}, Response: {e.response.text}")
            except Exception:
                error_detail += f" - {e.response.text}"
                print(f"Yoco HTTPStatusError: {error_detail}, Raw Response: {e.response.text}")
            raise HTTPException(status_code=500, detail="Failed to communicate with Yoco. Please try again later.")
        except httpx.RequestError as e:
            # Log this error on the server for debugging
            print(f"Yoco RequestError: {str(e)}")
            raise HTTPException(status_code=503, detail="Could not connect to Yoco payment service. Please try again later.")
        except Exception as e:
            # Log this error on the server for debugging
            print(f"Unexpected error during Yoco checkout: {str(e)}")
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred. Please try again later.")

@router.post("/subscription/create-checkout", response_model=YocoCheckoutResponse)
async def create_subscription_checkout(payment_request: SubscriptionPaymentRequest, db: Session = Depends(get_db)):
    """Create Yoco checkout for subscription payment"""
    if not YOCO_SECRET_KEY:
        raise HTTPException(status_code=500, detail="Payment system not configured")

    # Validate subscription tier
    if payment_request.subscription_tier.lower() not in SUBSCRIPTION_PRICING:
        raise HTTPException(status_code=400, detail="Invalid subscription tier")

    # Check if user already has an active subscription
    existing_subscriber = db.query(Subscriber).filter(
        Subscriber.email == payment_request.email,
        Subscriber.subscription_status == SubscriptionStatus.ACTIVE
    ).first()

    if existing_subscriber:
        raise HTTPException(status_code=400, detail="You already have an active subscription. Use the API key retrieval to get your key.")

    # Get pricing info
    pricing = SUBSCRIPTION_PRICING[payment_request.subscription_tier.lower()]
    amount_in_cents = int(pricing["amount"] * 100)

    # Create internal transaction ID
    internal_transaction_id = str(uuid.uuid4())

    # Store pending subscription data in database
    pending_subscriber = Subscriber(
        id=internal_transaction_id,
        email=payment_request.email,
        contact_name=payment_request.contact_name,
        company_name=payment_request.company_name,
        phone_number=payment_request.phone_number,
        subscription_tier=get_subscription_tier_enum(payment_request.subscription_tier),
        subscription_status=SubscriptionStatus.PENDING,
        monthly_fee=pricing["amount"],
        monthly_request_limit=pricing["requests"],
        created_at=datetime.utcnow()
    )

    db.add(pending_subscriber)
    db.commit()

    # Create Yoco checkout
    payload = {
        "amount": amount_in_cents,
        "currency": "ZAR",
        "successUrl": f"{APP_BASE_URL}/api/payments/subscription/callback/success?transaction_id={internal_transaction_id}",
        "cancelUrl": f"{APP_BASE_URL}/api/payments/subscription/callback/cancel?transaction_id={internal_transaction_id}",
        "failureUrl": f"{APP_BASE_URL}/api/payments/subscription/callback/failure?transaction_id={internal_transaction_id}",
        "metadata": {
            "internal_transaction_id": internal_transaction_id,
            "description": f"Hello Cyril API Subscription - {pricing['name']} - R{pricing['amount']:.2f}",
            "source": "HelloCyrilAPISubscription",
            "email": payment_request.email,
            "subscription_tier": payment_request.subscription_tier
        }
    }

    headers = {
        "Authorization": f"Bearer {YOCO_SECRET_KEY}",
        "Content-Type": "application/json",
        "Idempotency-Key": str(uuid.uuid4())
    }

    async with httpx.AsyncClient() as client:
        try:
            yoco_response = await client.post(YOCO_API_URL, json=payload, headers=headers)
            yoco_response.raise_for_status()
            yoco_checkout_data = yoco_response.json()

            if "redirectUrl" not in yoco_checkout_data:
                print(f"Yoco API Error: 'redirectUrl' not in response. Data: {yoco_checkout_data}")
                raise HTTPException(status_code=500, detail="Payment system error. Please try again later.")

            return YocoCheckoutResponse(
                redirectUrl=yoco_checkout_data["redirectUrl"],
                yocoCheckoutId=yoco_checkout_data.get("id")
            )

        except httpx.HTTPStatusError as e:
            error_detail = f"Error from Yoco API: {e.response.status_code}"
            try:
                error_body = e.response.json()
                error_message = error_body.get('message', error_body.get('detail', str(error_body)))
                error_detail += f" - {error_message}"
                print(f"Yoco HTTPStatusError: {error_detail}, Response: {e.response.text}")
            except Exception:
                error_detail += f" - {e.response.text}"
                print(f"Yoco HTTPStatusError: {error_detail}, Raw Response: {e.response.text}")
            raise HTTPException(status_code=500, detail="Payment system error. Please try again later.")
        except Exception as e:
            print(f"Unexpected error during subscription checkout: {str(e)}")
            raise HTTPException(status_code=500, detail="An unexpected error occurred. Please try again later.")

# Placeholder callback endpoints.
# IMPORTANT: Yoco recommends using webhooks to verify payment status reliably,
# not just relying on the user being redirected to the successUrl.
# These endpoints would typically show a message to the user and could trigger
# further backend processing if not solely relying on webhooks.

@router.get("/payment/yoco/callback/success")
async def yoco_payment_success(request: Request, transaction_id: str):
    # Track successful donation
    try:
        from app.api.analytics import track_donation
        from app.models.database import get_db

        # Get database session
        db = next(get_db())

        # Track the successful donation
        donation_data = {
            "amount": 0.0,  # Amount will be updated via webhook
            "payment_method": "yoco",
            "payment_status": "completed",
            "transaction_id": transaction_id,
            "payment_provider_id": transaction_id
        }

        await track_donation(request, donation_data, db)

    except Exception as e:
        # Don't fail the redirect if tracking fails
        print(f"Failed to track donation success: {str(e)}")

    # Redirect to the /stats page with query parameters
    stats_page_url = f"{APP_BASE_URL}/stats?payment_status=success&transaction_id={transaction_id}"
    return RedirectResponse(url=stats_page_url)

@router.get("/subscription/callback/success")
async def subscription_payment_success(request: Request, transaction_id: str, db: Session = Depends(get_db)):
    """Handle successful subscription payment and create API key"""
    try:
        # Find the pending subscriber
        subscriber = db.query(Subscriber).filter(Subscriber.id == transaction_id).first()

        if not subscriber:
            # Redirect to subscription page with error
            return RedirectResponse(url=f"{APP_BASE_URL}/subscription?error=subscription_not_found")

        if subscriber.subscription_status == SubscriptionStatus.ACTIVE:
            # Already processed, redirect to success page
            return RedirectResponse(url=f"{APP_BASE_URL}/subscription-success?subscriber_id={subscriber.id}")

        # Activate subscription
        subscriber.subscription_status = SubscriptionStatus.ACTIVE
        subscriber.billing_cycle_start = datetime.utcnow()
        subscriber.billing_cycle_end = datetime.utcnow() + timedelta(days=30)
        subscriber.next_billing_date = datetime.utcnow() + timedelta(days=30)

        # Generate API key
        api_key = generate_api_key()

        # Create API key record
        api_key_record = APIKey(
            id=str(uuid.uuid4()),
            subscriber_id=subscriber.id,
            key_name="Primary API Key",
            api_key_hash=api_key,  # In production, you'd hash this
            key_prefix=api_key[:8],
            status=APIKeyStatus.ACTIVE,
            created_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(days=365)  # 1 year expiry
        )

        db.add(api_key_record)
        db.commit()

        # Redirect to success page with API key
        return RedirectResponse(url=f"{APP_BASE_URL}/subscription-success?subscriber_id={subscriber.id}&api_key={api_key}")

    except Exception as e:
        print(f"Error processing subscription payment success: {str(e)}")
        return RedirectResponse(url=f"{APP_BASE_URL}/subscription?error=processing_failed")

@router.get("/subscription/callback/cancel")
async def subscription_payment_cancel(request: Request, transaction_id: str, db: Session = Depends(get_db)):
    """Handle cancelled subscription payment"""
    try:
        # Update subscriber status to cancelled
        subscriber = db.query(Subscriber).filter(Subscriber.id == transaction_id).first()
        if subscriber:
            subscriber.subscription_status = SubscriptionStatus.CANCELLED
            db.commit()
    except Exception as e:
        print(f"Error handling subscription cancellation: {str(e)}")

    return RedirectResponse(url=f"{APP_BASE_URL}/subscription?status=cancelled")

@router.get("/subscription/callback/failure")
async def subscription_payment_failure(request: Request, transaction_id: str, db: Session = Depends(get_db)):
    """Handle failed subscription payment"""
    try:
        # Update subscriber status to failed
        subscriber = db.query(Subscriber).filter(Subscriber.id == transaction_id).first()
        if subscriber:
            subscriber.subscription_status = SubscriptionStatus.CANCELLED
            db.commit()
    except Exception as e:
        print(f"Error handling subscription failure: {str(e)}")

    return RedirectResponse(url=f"{APP_BASE_URL}/subscription?status=failed")

@router.get("/payment/yoco/callback/cancel")
async def yoco_payment_cancel(request: Request, transaction_id: str):
    # Log this event, update transaction status.
    # Display a cancellation message or redirect
    # For consistency, redirecting to stats page with a status
    stats_page_url = f"{APP_BASE_URL}/stats?payment_status=cancelled&transaction_id={transaction_id}"
    return RedirectResponse(url=stats_page_url)

@router.get("/payment/yoco/callback/failure")
async def yoco_payment_failure(request: Request, transaction_id: str):
    # Log this event, update transaction status.
    # Display a failure message or redirect
    # For consistency, redirecting to stats page with a status
    stats_page_url = f"{APP_BASE_URL}/stats?payment_status=failure&transaction_id={transaction_id}"
    return RedirectResponse(url=stats_page_url)

@router.post("/subscription/retrieve-api-key", response_model=APIKeyResponse)
async def retrieve_api_key(retrieval_request: APIKeyRetrievalRequest, db: Session = Depends(get_db)):
    """Retrieve API key for existing subscriber"""

    # Find active subscriber
    subscriber = db.query(Subscriber).filter(
        Subscriber.email == retrieval_request.email,
        Subscriber.contact_name == retrieval_request.contact_name,
        Subscriber.subscription_status == SubscriptionStatus.ACTIVE
    ).first()

    if not subscriber:
        raise HTTPException(
            status_code=404,
            detail="No active subscription found for the provided email and contact name"
        )

    # Find active API key
    api_key_record = db.query(APIKey).filter(
        APIKey.subscriber_id == subscriber.id,
        APIKey.status == APIKeyStatus.ACTIVE
    ).first()

    if not api_key_record:
        raise HTTPException(
            status_code=404,
            detail="No active API key found. Please contact support."
        )

    # Check if API key is expired
    if api_key_record.expires_at and api_key_record.expires_at < datetime.utcnow():
        raise HTTPException(
            status_code=400,
            detail="Your API key has expired. Please renew your subscription."
        )

    return APIKeyResponse(
        api_key=api_key_record.api_key_hash,  # In production, this would be handled more securely
        subscriber_id=subscriber.id,
        subscription_tier=subscriber.subscription_tier.value,
        expires_at=api_key_record.expires_at.isoformat() if api_key_record.expires_at else "Never",
        monthly_limit=subscriber.monthly_request_limit
    )

