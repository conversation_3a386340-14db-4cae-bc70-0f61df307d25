from fastapi import <PERSON><PERSON><PERSON>, Request, Response, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_429_TOO_MANY_REQUESTS
import os
import time
from typing import Dict, List, Tuple, Optional
from dotenv import load_dotenv
from base64 import b64encode

from app.api import reports, groups, whatsapp, stats, contact_us, yoco_payments, auth, admin, analytics
from app.models.database import engine, Base, get_db
from sqlalchemy.orm import Session

# Import all models to ensure they're registered with SQLAlchemy
from app.models import report, group, message_log, admin as admin_model, analytics as analytics_model
from app.models import stats as stats_model
from app.api.auth import get_current_active_admin

class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware for rate limiting requests.

    This middleware limits the number of requests a client can make within a specified time window.
    It uses a sliding window algorithm to track requests.
    """
    def __init__(
        self,
        app,
        rate_limit: int = 60,  # requests per minute
        window_size: int = 60,  # window size in seconds
        block_time: int = 60,  # block time in seconds
        exclude_paths: Optional[List[str]] = None,
        exclude_methods: Optional[List[str]] = None,
        exclude_ips: Optional[List[str]] = None,
    ):
        super().__init__(app)
        self.rate_limit = rate_limit
        self.window_size = window_size
        self.block_time = block_time
        self.exclude_paths = exclude_paths or ["/static", "/favicon.ico", "/health"]
        self.exclude_methods = exclude_methods or ["OPTIONS"]
        self.exclude_ips = exclude_ips or ["127.0.0.1"]

        # Store client request history: {client_id: [(timestamp, count), ...]}
        self.request_history: Dict[str, List[Tuple[float, int]]] = {}

        # Store blocked clients: {client_id: unblock_time}
        self.blocked_clients: Dict[str, float] = {}

    async def dispatch(self, request: Request, call_next):
        # Get client identifier (IP address)
        client_id = self._get_client_id(request)

        # Skip rate limiting for excluded paths, methods, or IPs
        if self._should_exclude(request, client_id):
            return await call_next(request)

        # Check if client is blocked
        current_time = time.time()
        if client_id in self.blocked_clients:
            if current_time < self.blocked_clients[client_id]:
                # Client is still blocked
                return self._rate_limit_response(
                    retry_after=int(self.blocked_clients[client_id] - current_time)
                )
            else:
                # Unblock client
                del self.blocked_clients[client_id]

        # Check rate limit
        if self._is_rate_limited(client_id, current_time):
            # Block client
            self.blocked_clients[client_id] = current_time + self.block_time
            return self._rate_limit_response(retry_after=self.block_time)

        # Process the request
        return await call_next(request)

    def _get_client_id(self, request: Request) -> str:
        """Get a unique identifier for the client."""
        # Use X-Forwarded-For header if available (for clients behind proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Get the first IP in the chain (client's IP)
            return forwarded_for.split(",")[0].strip()

        # Otherwise use the client's direct IP
        return request.client.host if request.client else "unknown"

    def _should_exclude(self, request: Request, client_id: str) -> bool:
        """Check if the request should be excluded from rate limiting."""
        # Check if path is excluded
        for path in self.exclude_paths:
            if request.url.path.startswith(path):
                return True

        # Check if method is excluded
        if request.method in self.exclude_methods:
            return True

        # Check if IP is excluded
        if client_id in self.exclude_ips:
            return True

        return False

    def _is_rate_limited(self, client_id: str, current_time: float) -> bool:
        """Check if the client has exceeded the rate limit."""
        # Initialize client history if not exists
        if client_id not in self.request_history:
            self.request_history[client_id] = [(current_time, 1)]
            return False

        # Clean up old requests outside the window
        window_start = current_time - self.window_size
        self.request_history[client_id] = [
            (ts, count) for ts, count in self.request_history[client_id]
            if ts >= window_start
        ]

        # Count requests in the current window
        total_requests = sum(count for _, count in self.request_history[client_id])

        # Add current request
        self.request_history[client_id].append((current_time, 1))

        # Check if rate limit is exceeded
        return total_requests >= self.rate_limit

    def _rate_limit_response(self, retry_after: int) -> Response:
        """Create a rate limit exceeded response."""
        content = {
            "error": "Rate limit exceeded",
            "detail": f"Too many requests. Please try again after {retry_after} seconds."
        }

        response = Response(
            content=str(content),
            status_code=HTTP_429_TOO_MANY_REQUESTS,
            media_type="application/json"
        )

        # Set Retry-After header
        response.headers["Retry-After"] = str(retry_after)

        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security headers to responses.

    This middleware adds various security headers to HTTP responses to improve
    the security posture of the application.
    """
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net https://code.jquery.com; style-src 'self' 'unsafe-inline' https://unpkg.com https://fonts.googleapis.com https://cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' https://unpkg.com https://fonts.gstatic.com https://cdn.jsdelivr.net; connect-src 'self' https:; worker-src 'self' blob:;"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "camera=(), microphone=(), geolocation=(), interest-cohort=()"

        return response


# Load environment variables
load_dotenv()

# Get WhatsApp number from environment variables
WHATSAPP_NUMBER = os.getenv("WHATSAPP_NUMBER", "+27822923767")

# Create the database tables
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="Hello Cyril",
    description="A WhatsApp-based reporting system for crime, EMS services, and infrastructure issues",
    version="2.0.0",
    docs_url=None, redoc_url=None
    )

# Add security middleware (order matters - add security headers first)
app.add_middleware(SecurityHeadersMiddleware)

# Add rate limiting middleware
app.add_middleware(
    RateLimitMiddleware,
    rate_limit=100,  # 100 requests per minute
    window_size=60,  # 60 seconds window
    block_time=300,  # 5 minutes block time
    exclude_paths=["/static", "/favicon.ico", "/health", "/docs", "/openapi.json"],
    exclude_ips=["127.0.0.1", "::1"]  # Exclude localhost
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Set up Jinja2 templates
templates = Jinja2Templates(directory="app/templates")

# Custom Jinja2 filter to encode data in Base64
def b64encode_filter(data):
    return b64encode(data).decode('utf-8')

# Register the custom Jinja2 filter
templates.env.filters['b64encode'] = b64encode_filter

# Include routers
app.include_router(reports.router, prefix="/api/reports", tags=["reports"])
app.include_router(whatsapp.router, prefix="/api/whatsapp", tags=["whatsapp"])
app.include_router(groups.router, prefix="/api/groups", tags=["groups"])
app.include_router(stats.router, prefix="/api/stats", tags=["Statistics"])
app.include_router(contact_us.router, prefix="/api/contact", tags=["Contact Us"])
app.include_router(yoco_payments.router, prefix="/api/payments", tags=["Payments"]) # Added Yoco payments router

# Admin routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(admin.router, prefix="/api/admin", tags=["Admin"])
app.include_router(analytics.router, prefix="/api/analytics", tags=["Analytics"])

# Root endpoint
@app.get("/")
async def root(request: Request):
    # Page visit tracking is handled by analytics.js on the client side
    return templates.TemplateResponse("index.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

# Messages dashboard endpoint
@app.get("/messages")
async def messages_dashboard(request: Request):
    return templates.TemplateResponse("messages.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

# Map view endpoint
@app.get("/map")
async def map_view(request: Request):
    return templates.TemplateResponse("map.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

# Report generation endpoint
@app.get("/report")
async def report_generator(request: Request):
    return templates.TemplateResponse("report.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

# Community groups endpoint
@app.get("/groups")
async def community_groups(request: Request):
    return templates.TemplateResponse("groups.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

# Statistics endpoint
@app.get("/stats")
async def statistics_page(request: Request):
    return templates.TemplateResponse("stats.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

@app.get("/feedback")
async def contact_us(request: Request):
    return templates.TemplateResponse("contact_us.html", {"request": request})

@app.get("/easteregg")
async def easter_egg(request: Request):
    return templates.TemplateResponse("easteregg.html", {"request": request})

@app.get("/heatmap")
async def heatmap_view(request: Request):
    return templates.TemplateResponse("simple_heatmap.html", {"request": request})

# Admin page routes
@app.get("/admin/login")
async def admin_login(request: Request):
    return templates.TemplateResponse("admin/login.html", {"request": request})

@app.get("/admin/dashboard")
async def admin_dashboard(request: Request):
    return templates.TemplateResponse("admin/dashboard.html", {"request": request})

@app.get("/admin/admins")
async def admin_users(request: Request):
    return templates.TemplateResponse("admin/admins.html", {"request": request})

@app.get("/admin/feedback")
async def admin_feedback(request: Request):
    return templates.TemplateResponse("admin/feedback.html", {"request": request})

@app.get("/admin/groups")
async def admin_groups(request: Request):
    return templates.TemplateResponse("admin/groups.html", {"request": request})

@app.get("/admin/analytics")
async def admin_analytics(request: Request):
    return templates.TemplateResponse("admin/analytics.html", {"request": request})

# @app.get("/admin/debug")
# async def admin_debug(request: Request):
#     return templates.TemplateResponse("admin/debug.html", {"request": request})

# Protected WhatsApp endpoints (admin only)
@app.get("/admin/whatsapp-test")
async def admin_whatsapp_test(request: Request):
    return templates.TemplateResponse("whatsapp_test.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

@app.get("/admin/whatsapp")
async def admin_whatsapp(request: Request):
    return templates.TemplateResponse("whatsapp_test.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

# Redirect old WhatsApp routes to admin login
@app.get("/whatsapp")
async def whatsapp_redirect(request: Request):
    return templates.TemplateResponse("admin_required.html", {"request": request})

@app.get("/whatsapp-test")
async def whatsapp_test_redirect(request: Request):
    return templates.TemplateResponse("admin_required.html", {"request": request})

# # Test map endpoint
# @app.get("/test-map")
# async def test_map(request: Request):
#     return templates.TemplateResponse("test_map.html", {"request": request})

# WhatsApp test endpoint
# @app.get("/whatsapp-test")
# async def whatsapp_test_page(request: Request):
#     return templates.TemplateResponse("whatsapp_test.html", {"request": request, "whatsapp_number": WHATSAPP_NUMBER})

# Health check endpoint
@app.get("/health")
async def health():
    return {"status": "ok"}
