"""
Enhanced Security Middleware for Hello Cyril API
"""

from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import time
import hashlib
import re
from typing import Dict, List, Optional, Set
import logging
from datetime import datetime, timedelta
import ipaddress

logger = logging.getLogger(__name__)

class APISecurityMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive API security middleware
    """
    
    def __init__(
        self,
        app,
        # Rate limiting
        api_rate_limit: int = 100,  # requests per hour for API endpoints
        public_rate_limit: int = 1000,  # requests per hour for public pages
        burst_limit: int = 20,  # burst requests per minute
        
        # IP blocking
        blocked_ips: Optional[Set[str]] = None,
        blocked_countries: Optional[Set[str]] = None,
        
        # Content filtering
        max_request_size: int = 10 * 1024 * 1024,  # 10MB
        blocked_user_agents: Optional[List[str]] = None,
        
        # API protection
        protected_endpoints: Optional[List[str]] = None,
        test_endpoints: Optional[List[str]] = None,
    ):
        super().__init__(app)
        
        # Rate limiting
        self.api_rate_limit = api_rate_limit
        self.public_rate_limit = public_rate_limit
        self.burst_limit = burst_limit
        
        # IP tracking
        self.blocked_ips = blocked_ips or set()
        self.blocked_countries = blocked_countries or set()
        
        # Content filtering
        self.max_request_size = max_request_size
        self.blocked_user_agents = blocked_user_agents or [
            'bot', 'crawler', 'spider', 'scraper', 'scanner'
        ]
        
        # Endpoint protection
        self.protected_endpoints = protected_endpoints or [
            '/api/whatsapp/test',
            '/api/whatsapp/test-conversation',
            '/api/whatsapp/test-whatsapp-message',
            '/api/whatsapp/send'
        ]
        
        self.test_endpoints = test_endpoints or [
            '/api/whatsapp/test',
            '/api/whatsapp/test-conversation',
            '/api/whatsapp/test-whatsapp-message'
        ]
        
        # In-memory tracking (use Redis in production)
        self.request_counts: Dict[str, Dict[str, int]] = {}
        self.blocked_until: Dict[str, datetime] = {}
        self.suspicious_ips: Dict[str, int] = {}
        
    async def dispatch(self, request: Request, call_next):
        """Main security middleware logic"""
        
        # Get client info
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "").lower()
        path = request.url.path
        method = request.method
        
        try:
            # 1. Check if IP is blocked
            if await self._is_ip_blocked(client_ip):
                return self._security_response("IP blocked", 403)
            
            # 2. Check user agent
            if await self._is_user_agent_blocked(user_agent):
                return self._security_response("User agent blocked", 403)
            
            # 3. Check request size
            if await self._is_request_too_large(request):
                return self._security_response("Request too large", 413)
            
            # 4. Check test endpoints (block in production)
            if await self._is_test_endpoint_blocked(path):
                return self._security_response("Endpoint not available", 404)
            
            # 5. Rate limiting
            if await self._is_rate_limited(client_ip, path):
                return self._security_response("Rate limit exceeded", 429)
            
            # 6. Content validation for API endpoints
            if path.startswith('/api/') and method in ['POST', 'PUT', 'PATCH']:
                if not await self._validate_content(request):
                    return self._security_response("Invalid content", 400)
            
            # Process request
            response = await call_next(request)
            
            # 7. Track successful requests
            await self._track_request(client_ip, path, response.status_code)
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {str(e)}")
            return self._security_response("Security check failed", 500)
    
    def _get_client_ip(self, request: Request) -> str:
        """Get real client IP considering proxies"""
        # Check for forwarded headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    async def _is_ip_blocked(self, ip: str) -> bool:
        """Check if IP is blocked"""
        # Check explicit blocks
        if ip in self.blocked_ips:
            return True
        
        # Check if IP is currently blocked due to rate limiting
        if ip in self.blocked_until:
            if datetime.now() < self.blocked_until[ip]:
                return True
            else:
                del self.blocked_until[ip]
        
        # Check for suspicious activity
        if ip in self.suspicious_ips and self.suspicious_ips[ip] > 10:
            self.blocked_until[ip] = datetime.now() + timedelta(hours=1)
            return True
        
        return False
    
    async def _is_user_agent_blocked(self, user_agent: str) -> bool:
        """Check if user agent is blocked"""
        for blocked_agent in self.blocked_user_agents:
            if blocked_agent in user_agent:
                return True
        return False
    
    async def _is_request_too_large(self, request: Request) -> bool:
        """Check request size"""
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_request_size:
            return True
        return False
    
    async def _is_test_endpoint_blocked(self, path: str) -> bool:
        """Block test endpoints in production"""
        # Only allow test endpoints in development
        import os
        if os.getenv("ENVIRONMENT", "production") == "production":
            return path in self.test_endpoints
        return False
    
    async def _is_rate_limited(self, ip: str, path: str) -> bool:
        """Check rate limiting"""
        now = datetime.now()
        hour_key = now.strftime("%Y-%m-%d-%H")
        minute_key = now.strftime("%Y-%m-%d-%H-%M")
        
        # Initialize tracking
        if ip not in self.request_counts:
            self.request_counts[ip] = {}
        
        # Count requests
        hour_count = self.request_counts[ip].get(f"hour_{hour_key}", 0)
        minute_count = self.request_counts[ip].get(f"minute_{minute_key}", 0)
        
        # Determine limits
        if path.startswith('/api/'):
            hourly_limit = self.api_rate_limit
        else:
            hourly_limit = self.public_rate_limit
        
        # Check limits
        if hour_count >= hourly_limit:
            self._mark_suspicious(ip)
            return True
        
        if minute_count >= self.burst_limit:
            self._mark_suspicious(ip)
            return True
        
        # Update counts
        self.request_counts[ip][f"hour_{hour_key}"] = hour_count + 1
        self.request_counts[ip][f"minute_{minute_key}"] = minute_count + 1
        
        return False
    
    async def _validate_content(self, request: Request) -> bool:
        """Validate request content for API endpoints"""
        try:
            # Check content type
            content_type = request.headers.get("content-type", "")
            
            # For JSON endpoints
            if "application/json" in content_type:
                # Basic JSON validation happens in FastAPI
                pass
            
            # For form endpoints
            elif "application/x-www-form-urlencoded" in content_type:
                # Basic form validation
                pass
            
            return True
            
        except Exception:
            return False
    
    async def _track_request(self, ip: str, path: str, status_code: int):
        """Track request for analytics and security"""
        # Track failed requests
        if status_code >= 400:
            self._mark_suspicious(ip)
        
        # Clean old tracking data periodically
        if len(self.request_counts) > 10000:
            self._cleanup_tracking()
    
    def _mark_suspicious(self, ip: str):
        """Mark IP as suspicious"""
        if ip not in self.suspicious_ips:
            self.suspicious_ips[ip] = 0
        self.suspicious_ips[ip] += 1
        
        # Auto-block after too many suspicious activities
        if self.suspicious_ips[ip] > 20:
            self.blocked_until[ip] = datetime.now() + timedelta(hours=24)
    
    def _cleanup_tracking(self):
        """Clean old tracking data"""
        # Keep only recent data
        cutoff = datetime.now() - timedelta(hours=2)
        
        # This is a simple cleanup - in production use Redis with TTL
        for ip in list(self.request_counts.keys()):
            if len(self.request_counts[ip]) > 100:
                # Remove old entries
                self.request_counts[ip] = {}
    
    def _security_response(self, message: str, status_code: int) -> JSONResponse:
        """Return security response"""
        return JSONResponse(
            status_code=status_code,
            content={
                "error": "Security check failed",
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
        )


class InputSanitizationMiddleware(BaseHTTPMiddleware):
    """
    Input sanitization and validation middleware
    """
    
    def __init__(self, app):
        super().__init__(app)
        
        # Dangerous patterns
        self.sql_injection_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(--|#|/\*|\*/)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\bUNION\s+SELECT\b)",
        ]
        
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>.*?</iframe>",
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Sanitize input"""
        
        # Only check API endpoints
        if request.url.path.startswith('/api/'):
            # Check query parameters
            for key, value in request.query_params.items():
                if self._is_malicious_input(value):
                    return JSONResponse(
                        status_code=400,
                        content={"error": "Invalid input detected"}
                    )
        
        return await call_next(request)
    
    def _is_malicious_input(self, input_str: str) -> bool:
        """Check for malicious input patterns"""
        input_lower = input_str.lower()
        
        # Check SQL injection patterns
        for pattern in self.sql_injection_patterns:
            if re.search(pattern, input_lower, re.IGNORECASE):
                return True
        
        # Check XSS patterns
        for pattern in self.xss_patterns:
            if re.search(pattern, input_lower, re.IGNORECASE):
                return True
        
        return False
