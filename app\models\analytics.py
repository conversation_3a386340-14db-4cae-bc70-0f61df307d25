from sqlalchemy import Column, String, DateTime, Float, Text, Integer
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime

from app.models.database import Base

class PageVisit(Base):
    __tablename__ = "page_visits"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    ip_address = Column(String(45), nullable=False, index=True)  # IPv6 can be up to 45 chars
    user_agent = Column(Text, nullable=True)
    page_url = Column(String(500), nullable=False)
    page_title = Column(String(200), nullable=True)
    referrer = Column(String(500), nullable=True)
    visit_time = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    session_id = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)

    def __repr__(self):
        return f"<PageVisit(ip='{self.ip_address}', page='{self.page_url}', time='{self.visit_time}')>"

class WhatsAppClick(Base):
    __tablename__ = "whatsapp_clicks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    ip_address = Column(String(45), nullable=False, index=True)
    user_agent = Column(Text, nullable=True)
    click_source = Column(String(100), nullable=False)  # 'navbar', 'homepage', 'footer', etc.
    whatsapp_number = Column(String(20), nullable=True)
    click_time = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    session_id = Column(String(100), nullable=True)
    referrer_page = Column(String(500), nullable=True)
    country = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)

    def __repr__(self):
        return f"<WhatsAppClick(ip='{self.ip_address}', source='{self.click_source}', time='{self.click_time}')>"

class DonationTracking(Base):
    __tablename__ = "donation_tracking"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    ip_address = Column(String(45), nullable=False, index=True)
    user_agent = Column(Text, nullable=True)
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default='ZAR', nullable=False)
    payment_method = Column(String(50), nullable=False)  # 'yoco', 'paypal', etc.
    payment_status = Column(String(20), nullable=False)  # 'initiated', 'completed', 'failed', 'cancelled'
    transaction_id = Column(String(100), nullable=True)
    payment_provider_id = Column(String(100), nullable=True)  # External payment ID
    donation_time = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    completion_time = Column(DateTime, nullable=True)
    session_id = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)

    def __repr__(self):
        return f"<DonationTracking(ip='{self.ip_address}', amount='{self.amount}', status='{self.payment_status}', time='{self.donation_time}')>"
