"""
Subscription service models for Hello Cyril API access
"""

from sqlalchemy import Column, String, Float, <PERSON>olean, DateTime, Integer, Text, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime, timedelta
import enum
import hashlib
import secrets

from app.models.database import Base


class SubscriptionTier(enum.Enum):
    """Subscription tier levels"""
    BASIC = "basic"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"


class SubscriptionStatus(enum.Enum):
    """Subscription status"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    CANCELLED = "cancelled"
    EXPIRED = "expired"
    PENDING = "pending"


class APIKeyStatus(enum.Enum):
    """API key status"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    REVOKED = "revoked"


class Subscriber(Base):
    """Subscriber model for API access"""
    __tablename__ = "subscribers"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User information
    email = Column(String(255), unique=True, nullable=False, index=True)
    company_name = Column(String(255), nullable=True)
    contact_name = Column(String(255), nullable=False)
    phone_number = Column(String(20), nullable=True)
    
    # Subscription details
    subscription_tier = Column(Enum(SubscriptionTier), nullable=False, default=SubscriptionTier.BASIC)
    subscription_status = Column(Enum(SubscriptionStatus), nullable=False, default=SubscriptionStatus.PENDING)
    
    # Billing
    monthly_fee = Column(Float, nullable=False, default=99.0)  # ZAR
    billing_cycle_start = Column(DateTime(timezone=True), nullable=True)
    billing_cycle_end = Column(DateTime(timezone=True), nullable=True)
    next_billing_date = Column(DateTime(timezone=True), nullable=True)
    
    # Usage limits
    monthly_request_limit = Column(Integer, nullable=False, default=10000)
    current_month_requests = Column(Integer, nullable=False, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_payment_date = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    api_keys = relationship("APIKey", back_populates="subscriber", cascade="all, delete-orphan")
    usage_logs = relationship("APIUsageLog", back_populates="subscriber", cascade="all, delete-orphan")
    payments = relationship("SubscriptionPayment", back_populates="subscriber", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Subscriber {self.email} - {self.subscription_tier.value}>"
    
    def is_active(self) -> bool:
        """Check if subscription is active"""
        return (
            self.subscription_status == SubscriptionStatus.ACTIVE and
            self.billing_cycle_end and
            self.billing_cycle_end > datetime.utcnow()
        )
    
    def can_make_request(self) -> bool:
        """Check if subscriber can make API requests"""
        return (
            self.is_active() and
            self.current_month_requests < self.monthly_request_limit
        )
    
    def increment_usage(self):
        """Increment monthly usage counter"""
        self.current_month_requests += 1
    
    def reset_monthly_usage(self):
        """Reset monthly usage counter"""
        self.current_month_requests = 0
    
    def get_tier_limits(self) -> dict:
        """Get limits based on subscription tier"""
        tier_configs = {
            SubscriptionTier.BASIC: {
                "monthly_requests": 10000,
                "rate_limit_per_minute": 100,
                "features": ["reports", "stats"],
                "monthly_fee": 99.0
            },
            SubscriptionTier.PREMIUM: {
                "monthly_requests": 50000,
                "rate_limit_per_minute": 500,
                "features": ["reports", "stats", "groups", "real_time"],
                "monthly_fee": 299.0
            },
            SubscriptionTier.ENTERPRISE: {
                "monthly_requests": 200000,
                "rate_limit_per_minute": 2000,
                "features": ["reports", "stats", "groups", "real_time", "webhooks", "priority_support"],
                "monthly_fee": 999.0
            }
        }
        return tier_configs.get(self.subscription_tier, tier_configs[SubscriptionTier.BASIC])


class APIKey(Base):
    """API key model for authentication"""
    __tablename__ = "api_keys"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Key details
    key_name = Column(String(255), nullable=False)
    key_hash = Column(String(255), nullable=False, unique=True, index=True)
    key_prefix = Column(String(20), nullable=False)  # First few chars for identification
    
    # Status and permissions
    status = Column(Enum(APIKeyStatus), nullable=False, default=APIKeyStatus.ACTIVE)
    permissions = Column(Text, nullable=True)  # JSON string of permissions
    
    # Usage tracking
    total_requests = Column(Integer, nullable=False, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Foreign key
    subscriber_id = Column(UUID(as_uuid=True), ForeignKey("subscribers.id"), nullable=False)
    
    # Relationships
    subscriber = relationship("Subscriber", back_populates="api_keys")
    usage_logs = relationship("APIUsageLog", back_populates="api_key", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<APIKey {self.key_prefix}... - {self.key_name}>"
    
    @staticmethod
    def generate_api_key() -> tuple[str, str, str]:
        """Generate a new API key"""
        # Generate random key
        key = f"hc_{secrets.token_urlsafe(32)}"
        
        # Create hash for storage
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        
        # Get prefix for identification
        key_prefix = key[:12]
        
        return key, key_hash, key_prefix
    
    @staticmethod
    def hash_key(key: str) -> str:
        """Hash an API key for storage"""
        return hashlib.sha256(key.encode()).hexdigest()
    
    def is_valid(self) -> bool:
        """Check if API key is valid"""
        return (
            self.status == APIKeyStatus.ACTIVE and
            (not self.expires_at or self.expires_at > datetime.utcnow())
        )
    
    def increment_usage(self):
        """Increment usage counter"""
        self.total_requests += 1
        self.last_used_at = datetime.utcnow()


class APIUsageLog(Base):
    """API usage logging for billing and analytics"""
    __tablename__ = "api_usage_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Request details
    endpoint = Column(String(255), nullable=False)
    method = Column(String(10), nullable=False)
    ip_address = Column(String(45), nullable=False)
    user_agent = Column(Text, nullable=True)
    
    # Response details
    status_code = Column(Integer, nullable=False)
    response_time_ms = Column(Integer, nullable=True)
    response_size_bytes = Column(Integer, nullable=True)
    
    # Timestamps
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # Foreign keys
    subscriber_id = Column(UUID(as_uuid=True), ForeignKey("subscribers.id"), nullable=False)
    api_key_id = Column(UUID(as_uuid=True), ForeignKey("api_keys.id"), nullable=False)
    
    # Relationships
    subscriber = relationship("Subscriber", back_populates="usage_logs")
    api_key = relationship("APIKey", back_populates="usage_logs")
    
    def __repr__(self):
        return f"<APIUsageLog {self.endpoint} - {self.status_code}>"


class SubscriptionPayment(Base):
    """Payment tracking for subscriptions"""
    __tablename__ = "subscription_payments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Payment details
    amount = Column(Float, nullable=False)
    currency = Column(String(3), nullable=False, default="ZAR")
    payment_method = Column(String(50), nullable=False)
    
    # Payment status
    payment_status = Column(String(50), nullable=False)
    transaction_id = Column(String(255), nullable=True)
    payment_provider_id = Column(String(255), nullable=True)
    
    # Billing period
    billing_period_start = Column(DateTime(timezone=True), nullable=False)
    billing_period_end = Column(DateTime(timezone=True), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    paid_at = Column(DateTime(timezone=True), nullable=True)
    
    # Foreign key
    subscriber_id = Column(UUID(as_uuid=True), ForeignKey("subscribers.id"), nullable=False)
    
    # Relationships
    subscriber = relationship("Subscriber", back_populates="payments")
    
    def __repr__(self):
        return f"<SubscriptionPayment R{self.amount} - {self.payment_status}>"


class AlertSubscription(Base):
    """Real-time alert subscriptions"""
    __tablename__ = "alert_subscriptions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Subscription details
    webhook_url = Column(String(500), nullable=False)
    alert_types = Column(Text, nullable=False)  # JSON array of alert types
    geographic_filter = Column(Text, nullable=True)  # JSON geographic bounds
    
    # Status
    is_active = Column(Boolean, nullable=False, default=True)
    last_delivery_attempt = Column(DateTime(timezone=True), nullable=True)
    failed_deliveries = Column(Integer, nullable=False, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Foreign key
    subscriber_id = Column(UUID(as_uuid=True), ForeignKey("subscribers.id"), nullable=False)
    
    # Relationships
    subscriber = relationship("Subscriber")
    
    def __repr__(self):
        return f"<AlertSubscription {self.webhook_url}>"
