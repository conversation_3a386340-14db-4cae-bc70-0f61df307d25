"""
Pydantic schemas for subscription service
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

from app.models.subscription import SubscriptionTier, SubscriptionStatus, APIKeyStatus


class SubscriberCreate(BaseModel):
    """Schema for creating a new subscriber"""
    email: EmailStr
    company_name: Optional[str] = None
    contact_name: str
    phone_number: Optional[str] = None
    subscription_tier: Optional[SubscriptionTier] = SubscriptionTier.BASIC
    
    @validator('phone_number')
    def validate_phone_number(cls, v):
        if v and not v.startswith('+27'):
            raise ValueError('Phone number must be in South African format (+27xxxxxxxxx)')
        return v


class SubscriberResponse(BaseModel):
    """Schema for subscriber response"""
    id: str
    email: str
    company_name: Optional[str]
    contact_name: str
    phone_number: Optional[str]
    subscription_tier: SubscriptionTier
    subscription_status: SubscriptionStatus
    monthly_fee: float
    monthly_request_limit: int
    current_month_requests: int
    created_at: datetime
    billing_cycle_start: Optional[datetime]
    billing_cycle_end: Optional[datetime]
    next_billing_date: Optional[datetime]
    
    class Config:
        from_attributes = True


class SubscriptionUpdate(BaseModel):
    """Schema for updating subscription"""
    subscription_tier: Optional[SubscriptionTier] = None
    company_name: Optional[str] = None
    contact_name: Optional[str] = None
    phone_number: Optional[str] = None


class APIKeyCreate(BaseModel):
    """Schema for creating API key"""
    key_name: str
    permissions: Optional[List[str]] = None
    expires_at: Optional[datetime] = None
    
    @validator('key_name')
    def validate_key_name(cls, v):
        if len(v) < 3 or len(v) > 50:
            raise ValueError('Key name must be between 3 and 50 characters')
        return v


class APIKeyResponse(BaseModel):
    """Schema for API key response"""
    id: str
    key_name: str
    key_prefix: str
    status: APIKeyStatus
    created_at: datetime
    expires_at: Optional[datetime]
    last_used_at: Optional[datetime]
    total_requests: int
    api_key: Optional[str] = None  # Only returned on creation
    
    class Config:
        from_attributes = True


class UsageStatsResponse(BaseModel):
    """Schema for usage statistics"""
    subscriber_id: str
    period_days: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    current_month_usage: int
    monthly_limit: int
    usage_percentage: float
    endpoint_breakdown: Dict[str, int]
    daily_breakdown: Dict[str, int]
    subscription_tier: str
    subscription_status: str


class PaymentCreate(BaseModel):
    """Schema for creating payment"""
    amount: float
    payment_method: str
    billing_period_start: datetime
    billing_period_end: datetime


class PaymentResponse(BaseModel):
    """Schema for payment response"""
    id: str
    amount: float
    currency: str
    payment_method: str
    payment_status: str
    transaction_id: Optional[str]
    billing_period_start: datetime
    billing_period_end: datetime
    created_at: datetime
    paid_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class AlertSubscriptionCreate(BaseModel):
    """Schema for creating alert subscription"""
    webhook_url: str
    alert_types: List[str]
    geographic_filter: Optional[Dict[str, Any]] = None
    
    @validator('webhook_url')
    def validate_webhook_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Webhook URL must be a valid HTTP/HTTPS URL')
        return v
    
    @validator('alert_types')
    def validate_alert_types(cls, v):
        valid_types = ['crime', 'ems', 'infrastructure', 'all']
        for alert_type in v:
            if alert_type not in valid_types:
                raise ValueError(f'Invalid alert type: {alert_type}')
        return v


class AlertSubscriptionResponse(BaseModel):
    """Schema for alert subscription response"""
    id: str
    webhook_url: str
    alert_types: List[str]
    geographic_filter: Optional[Dict[str, Any]]
    is_active: bool
    created_at: datetime
    last_delivery_attempt: Optional[datetime]
    failed_deliveries: int
    
    class Config:
        from_attributes = True


class SubscriptionPlan(BaseModel):
    """Schema for subscription plan details"""
    tier: SubscriptionTier
    name: str
    description: str
    monthly_fee: float
    monthly_requests: int
    rate_limit_per_minute: int
    features: List[str]
    
    @classmethod
    def get_all_plans(cls) -> List['SubscriptionPlan']:
        """Get all available subscription plans"""
        return [
            cls(
                tier=SubscriptionTier.BASIC,
                name="Basic Plan",
                description="Perfect for small applications and testing",
                monthly_fee=99.0,
                monthly_requests=10000,
                rate_limit_per_minute=100,
                features=[
                    "Access to reports API",
                    "Basic statistics",
                    "Email support",
                    "10,000 requests/month"
                ]
            ),
            cls(
                tier=SubscriptionTier.PREMIUM,
                name="Premium Plan",
                description="Ideal for growing businesses and applications",
                monthly_fee=299.0,
                monthly_requests=50000,
                rate_limit_per_minute=500,
                features=[
                    "Access to reports API",
                    "Advanced statistics",
                    "Community groups API",
                    "Real-time alerts",
                    "Priority email support",
                    "50,000 requests/month"
                ]
            ),
            cls(
                tier=SubscriptionTier.ENTERPRISE,
                name="Enterprise Plan",
                description="For large-scale applications and enterprise use",
                monthly_fee=999.0,
                monthly_requests=200000,
                rate_limit_per_minute=2000,
                features=[
                    "Full API access",
                    "Real-time webhooks",
                    "Custom geographic filters",
                    "Priority support",
                    "SLA guarantee",
                    "Custom integrations",
                    "200,000 requests/month"
                ]
            )
        ]


class APIErrorResponse(BaseModel):
    """Schema for API error responses"""
    error: str
    message: str
    timestamp: datetime
    request_id: Optional[str] = None


class APISuccessResponse(BaseModel):
    """Schema for API success responses"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: datetime


class WebhookPayload(BaseModel):
    """Schema for webhook payloads"""
    event_type: str
    timestamp: datetime
    data: Dict[str, Any]
    subscriber_id: str
    
    class Config:
        schema_extra = {
            "example": {
                "event_type": "new_report",
                "timestamp": "2024-01-01T12:00:00Z",
                "data": {
                    "report_id": "123e4567-e89b-12d3-a456-426614174000",
                    "category": "crime",
                    "location": {
                        "latitude": -25.7461,
                        "longitude": 28.1881
                    },
                    "description": "Suspicious activity reported"
                },
                "subscriber_id": "123e4567-e89b-12d3-a456-426614174000"
            }
        }
