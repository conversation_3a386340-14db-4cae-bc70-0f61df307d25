"""
Real-time alert service using Redis for subscription API
"""

import redis
import json
import asyncio
import aiohttp
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.subscription import Subscriber, AlertSubscription
from app.models.report import Report

logger = logging.getLogger(__name__)


class AlertService:
    """Service for managing real-time alerts to subscribers"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        """Initialize alert service with Redis connection"""
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.pubsub = self.redis_client.pubsub()
        
        # Alert channels
        self.ALERT_CHANNEL = "hello_cyril:alerts"
        self.WEBHOOK_QUEUE = "hello_cyril:webhooks"
        
    async def publish_new_report_alert(self, report: Report, db: Session):
        """Publish alert for new report to all subscribers"""
        try:
            # Create alert payload
            alert_data = {
                "event_type": "new_report",
                "timestamp": datetime.utcnow().isoformat(),
                "data": {
                    "report_id": str(report.id),
                    "category": report.category.value,
                    "description": report.description,
                    "location": {
                        "latitude": report.latitude,
                        "longitude": report.longitude
                    },
                    "timestamp": report.timestamp.isoformat(),
                    "verified": report.verified
                }
            }
            
            # Publish to Redis channel
            self.redis_client.publish(
                self.ALERT_CHANNEL,
                json.dumps(alert_data)
            )
            
            # Queue webhooks for subscribers
            await self._queue_webhooks_for_report(report, alert_data, db)
            
            logger.info(f"Published alert for report {report.id}")
            
        except Exception as e:
            logger.error(f"Failed to publish alert for report {report.id}: {str(e)}")
    
    async def _queue_webhooks_for_report(self, report: Report, alert_data: Dict, db: Session):
        """Queue webhook deliveries for subscribers interested in this report"""
        try:
            # Get all active alert subscriptions
            subscriptions = db.query(AlertSubscription).filter(
                AlertSubscription.is_active == True
            ).all()
            
            for subscription in subscriptions:
                # Check if subscriber is interested in this alert
                if await self._should_send_alert(subscription, report):
                    # Add subscriber info to alert data
                    webhook_payload = {
                        **alert_data,
                        "subscriber_id": str(subscription.subscriber_id),
                        "webhook_url": subscription.webhook_url
                    }
                    
                    # Queue webhook delivery
                    self.redis_client.lpush(
                        self.WEBHOOK_QUEUE,
                        json.dumps(webhook_payload)
                    )
                    
                    logger.debug(f"Queued webhook for subscriber {subscription.subscriber_id}")
                    
        except Exception as e:
            logger.error(f"Failed to queue webhooks: {str(e)}")
    
    async def _should_send_alert(self, subscription: AlertSubscription, report: Report) -> bool:
        """Check if alert should be sent to this subscription"""
        try:
            # Parse alert types
            alert_types = json.loads(subscription.alert_types)
            
            # Check if report category matches subscription
            if "all" not in alert_types and report.category.value not in alert_types:
                return False
            
            # Check geographic filter if set
            if subscription.geographic_filter:
                geo_filter = json.loads(subscription.geographic_filter)
                if not await self._is_within_geographic_bounds(report, geo_filter):
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking alert criteria: {str(e)}")
            return False
    
    async def _is_within_geographic_bounds(self, report: Report, geo_filter: Dict) -> bool:
        """Check if report is within geographic bounds"""
        try:
            # Simple bounding box check
            if "bounds" in geo_filter:
                bounds = geo_filter["bounds"]
                return (
                    bounds["south"] <= report.latitude <= bounds["north"] and
                    bounds["west"] <= report.longitude <= bounds["east"]
                )
            
            # Radius check from center point
            if "center" in geo_filter and "radius_km" in geo_filter:
                center = geo_filter["center"]
                radius_km = geo_filter["radius_km"]
                
                # Simple distance calculation (not precise for large distances)
                lat_diff = abs(report.latitude - center["latitude"])
                lng_diff = abs(report.longitude - center["longitude"])
                distance_approx = ((lat_diff ** 2) + (lng_diff ** 2)) ** 0.5 * 111  # Rough km conversion
                
                return distance_approx <= radius_km
            
            return True  # No geographic filter
            
        except Exception as e:
            logger.error(f"Error checking geographic bounds: {str(e)}")
            return True  # Default to sending if error
    
    async def process_webhook_queue(self, db: Session):
        """Process queued webhook deliveries"""
        """This should run as a background task"""
        while True:
            try:
                # Get webhook from queue (blocking with timeout)
                webhook_data = self.redis_client.brpop(self.WEBHOOK_QUEUE, timeout=5)
                
                if webhook_data:
                    _, payload_json = webhook_data
                    payload = json.loads(payload_json)
                    
                    await self._deliver_webhook(payload, db)
                
            except Exception as e:
                logger.error(f"Error processing webhook queue: {str(e)}")
                await asyncio.sleep(1)
    
    async def _deliver_webhook(self, payload: Dict, db: Session):
        """Deliver webhook to subscriber"""
        try:
            webhook_url = payload["webhook_url"]
            subscriber_id = payload["subscriber_id"]
            
            # Remove internal fields
            clean_payload = {k: v for k, v in payload.items() 
                           if k not in ["webhook_url", "subscriber_id"]}
            
            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=clean_payload,
                    headers={"Content-Type": "application/json"},
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status == 200:
                        logger.info(f"Webhook delivered successfully to {webhook_url}")
                        await self._update_delivery_success(subscriber_id, db)
                    else:
                        logger.warning(f"Webhook delivery failed: {response.status} for {webhook_url}")
                        await self._update_delivery_failure(subscriber_id, db)
                        
        except asyncio.TimeoutError:
            logger.warning(f"Webhook delivery timeout for {webhook_url}")
            await self._update_delivery_failure(subscriber_id, db)
        except Exception as e:
            logger.error(f"Webhook delivery error for {webhook_url}: {str(e)}")
            await self._update_delivery_failure(subscriber_id, db)
    
    async def _update_delivery_success(self, subscriber_id: str, db: Session):
        """Update successful delivery stats"""
        try:
            subscription = db.query(AlertSubscription).filter(
                AlertSubscription.subscriber_id == subscriber_id
            ).first()
            
            if subscription:
                subscription.last_delivery_attempt = datetime.utcnow()
                subscription.failed_deliveries = 0  # Reset failure count
                db.commit()
                
        except Exception as e:
            logger.error(f"Failed to update delivery success: {str(e)}")
    
    async def _update_delivery_failure(self, subscriber_id: str, db: Session):
        """Update failed delivery stats"""
        try:
            subscription = db.query(AlertSubscription).filter(
                AlertSubscription.subscriber_id == subscriber_id
            ).first()
            
            if subscription:
                subscription.last_delivery_attempt = datetime.utcnow()
                subscription.failed_deliveries += 1
                
                # Disable subscription after too many failures
                if subscription.failed_deliveries >= 10:
                    subscription.is_active = False
                    logger.warning(f"Disabled alert subscription for subscriber {subscriber_id} due to repeated failures")
                
                db.commit()
                
        except Exception as e:
            logger.error(f"Failed to update delivery failure: {str(e)}")
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """Get alert service statistics"""
        try:
            return {
                "redis_connected": self.redis_client.ping(),
                "queued_webhooks": self.redis_client.llen(self.WEBHOOK_QUEUE),
                "active_subscribers": self.redis_client.get("alert_service:active_subscribers") or 0,
                "total_alerts_sent": self.redis_client.get("alert_service:total_sent") or 0,
                "failed_deliveries": self.redis_client.get("alert_service:failed_deliveries") or 0
            }
        except Exception as e:
            logger.error(f"Failed to get alert stats: {str(e)}")
            return {"error": str(e)}
    
    async def subscribe_to_alerts(self, callback_func):
        """Subscribe to real-time alerts (for WebSocket connections)"""
        try:
            self.pubsub.subscribe(self.ALERT_CHANNEL)
            
            for message in self.pubsub.listen():
                if message["type"] == "message":
                    alert_data = json.loads(message["data"])
                    await callback_func(alert_data)
                    
        except Exception as e:
            logger.error(f"Error in alert subscription: {str(e)}")
    
    def close(self):
        """Close Redis connections"""
        try:
            self.pubsub.close()
            self.redis_client.close()
        except Exception as e:
            logger.error(f"Error closing Redis connections: {str(e)}")


# Global alert service instance
alert_service = None

def get_alert_service() -> AlertService:
    """Get global alert service instance"""
    global alert_service
    if alert_service is None:
        import os
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        alert_service = AlertService(redis_url)
    return alert_service
