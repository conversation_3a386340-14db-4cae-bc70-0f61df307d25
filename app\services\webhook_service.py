"""
Webhook Service for Hello Cyril API
Handles webhook delivery and testing
"""

import aiohttp
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.orm import Session

from app.models.subscription import AlertSubscription, Subscriber
from app.models.database import get_db

logger = logging.getLogger(__name__)


class WebhookService:
    """Service for managing webhook deliveries"""
    
    def __init__(self):
        self.session_timeout = aiohttp.ClientTimeout(total=10)
        
    async def send_webhook(self, webhook_url: str, payload: Dict, 
                          headers: Optional[Dict] = None) -> Dict:
        """
        Send webhook to specified URL
        
        Args:
            webhook_url: Target webhook URL
            payload: JSON payload to send
            headers: Optional additional headers
            
        Returns:
            Dict with delivery status and response info
        """
        if headers is None:
            headers = {}
            
        # Default headers
        default_headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'HelloCyril-Webhook/1.0',
            'X-Webhook-Source': 'hellocyril.co.za'
        }
        default_headers.update(headers)
        
        try:
            async with aiohttp.ClientSession(timeout=self.session_timeout) as session:
                async with session.post(
                    webhook_url,
                    json=payload,
                    headers=default_headers
                ) as response:
                    
                    response_text = await response.text()
                    
                    return {
                        'success': response.status == 200,
                        'status_code': response.status,
                        'response_text': response_text[:1000],  # Limit response size
                        'delivery_time': datetime.utcnow().isoformat(),
                        'error': None
                    }
                    
        except asyncio.TimeoutError:
            return {
                'success': False,
                'status_code': 408,
                'response_text': 'Request timeout',
                'delivery_time': datetime.utcnow().isoformat(),
                'error': 'timeout'
            }
        except Exception as e:
            return {
                'success': False,
                'status_code': 0,
                'response_text': str(e),
                'delivery_time': datetime.utcnow().isoformat(),
                'error': str(e)
            }
    
    async def send_alert_webhook(self, alert_data: Dict, db: Session) -> List[Dict]:
        """
        Send alert to all active webhook subscriptions
        
        Args:
            alert_data: Alert information
            db: Database session
            
        Returns:
            List of delivery results
        """
        # Get all active webhook subscriptions
        active_webhooks = db.query(AlertSubscription).filter(
            AlertSubscription.is_active == True
        ).all()
        
        if not active_webhooks:
            logger.info("No active webhook subscriptions found")
            return []
        
        # Prepare webhook payload
        webhook_payload = {
            'event_type': 'new_report',
            'timestamp': datetime.utcnow().isoformat(),
            'data': alert_data
        }
        
        delivery_results = []
        
        # Send to each webhook
        for webhook in active_webhooks:
            try:
                # Check if alert matches subscription filters
                if not self._matches_filters(alert_data, webhook):
                    continue
                
                # Send webhook
                result = await self.send_webhook(
                    webhook.webhook_url,
                    webhook_payload
                )
                
                # Update webhook statistics
                webhook.last_delivery_attempt = datetime.utcnow()
                
                if result['success']:
                    webhook.successful_deliveries += 1
                    webhook.failed_deliveries = 0  # Reset failed count on success
                    logger.info(f"Webhook delivered successfully to {webhook.webhook_url}")
                else:
                    webhook.failed_deliveries += 1
                    logger.warning(f"Webhook delivery failed to {webhook.webhook_url}: {result['error']}")
                    
                    # Disable webhook after 5 consecutive failures
                    if webhook.failed_deliveries >= 5:
                        webhook.is_active = False
                        logger.warning(f"Webhook disabled due to repeated failures: {webhook.webhook_url}")
                
                delivery_results.append({
                    'webhook_url': webhook.webhook_url,
                    'subscriber_id': webhook.subscriber_id,
                    **result
                })
                
            except Exception as e:
                logger.error(f"Error processing webhook {webhook.webhook_url}: {str(e)}")
                webhook.failed_deliveries += 1
                
                delivery_results.append({
                    'webhook_url': webhook.webhook_url,
                    'subscriber_id': webhook.subscriber_id,
                    'success': False,
                    'error': str(e)
                })
        
        # Commit webhook statistics updates
        db.commit()
        
        return delivery_results
    
    def _matches_filters(self, alert_data: Dict, webhook: AlertSubscription) -> bool:
        """
        Check if alert matches webhook subscription filters
        
        Args:
            alert_data: Alert information
            webhook: Webhook subscription
            
        Returns:
            True if alert matches filters
        """
        # Parse alert types filter
        try:
            alert_types = json.loads(webhook.alert_types) if webhook.alert_types else ['all']
        except (json.JSONDecodeError, TypeError):
            alert_types = ['all']
        
        # Check alert type filter
        if 'all' not in alert_types:
            alert_category = alert_data.get('category', '').lower()
            if alert_category not in [t.lower() for t in alert_types]:
                return False
        
        # Check geographic filter
        if webhook.geographic_filter:
            try:
                geo_filter = json.loads(webhook.geographic_filter)
                if not self._matches_geographic_filter(alert_data, geo_filter):
                    return False
            except (json.JSONDecodeError, TypeError):
                pass  # Ignore invalid geographic filters
        
        return True
    
    def _matches_geographic_filter(self, alert_data: Dict, geo_filter: Dict) -> bool:
        """
        Check if alert location matches geographic filter
        
        Args:
            alert_data: Alert information
            geo_filter: Geographic filter configuration
            
        Returns:
            True if location matches filter
        """
        alert_location = alert_data.get('location', {})
        alert_lat = alert_location.get('latitude')
        alert_lng = alert_location.get('longitude')
        
        if alert_lat is None or alert_lng is None:
            return True  # Include alerts without location data
        
        # Check radius filter
        if 'center' in geo_filter and 'radius_km' in geo_filter:
            center = geo_filter['center']
            center_lat = center.get('latitude')
            center_lng = center.get('longitude')
            radius_km = geo_filter.get('radius_km', 0)
            
            if center_lat is not None and center_lng is not None:
                distance = self._calculate_distance(
                    alert_lat, alert_lng,
                    center_lat, center_lng
                )
                return distance <= radius_km
        
        # Check bounding box filter
        if 'bounds' in geo_filter:
            bounds = geo_filter['bounds']
            north = bounds.get('north')
            south = bounds.get('south')
            east = bounds.get('east')
            west = bounds.get('west')
            
            if all(coord is not None for coord in [north, south, east, west]):
                return (south <= alert_lat <= north and 
                       west <= alert_lng <= east)
        
        return True  # Include if no valid geographic filter
    
    def _calculate_distance(self, lat1: float, lng1: float, 
                           lat2: float, lng2: float) -> float:
        """
        Calculate distance between two points using Haversine formula
        
        Args:
            lat1, lng1: First point coordinates
            lat2, lng2: Second point coordinates
            
        Returns:
            Distance in kilometers
        """
        import math
        
        # Convert to radians
        lat1, lng1, lat2, lng2 = map(math.radians, [lat1, lng1, lat2, lng2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = (math.sin(dlat/2)**2 + 
             math.cos(lat1) * math.cos(lat2) * math.sin(dlng/2)**2)
        c = 2 * math.asin(math.sqrt(a))
        
        # Earth's radius in kilometers
        r = 6371
        
        return c * r
    
    async def test_webhook(self, webhook_url: str, subscriber: Subscriber) -> Dict:
        """
        Send test webhook to verify endpoint
        
        Args:
            webhook_url: Target webhook URL
            subscriber: Subscriber information
            
        Returns:
            Test result
        """
        test_payload = {
            'event_type': 'test_webhook',
            'timestamp': datetime.utcnow().isoformat(),
            'data': {
                'message': 'This is a test webhook from Hello Cyril API',
                'test': True,
                'subscriber_id': subscriber.id,
                'subscription_tier': subscriber.subscription_tier.value
            }
        }
        
        result = await self.send_webhook(webhook_url, test_payload)
        
        return {
            'message': 'Test webhook sent',
            'webhook_url': webhook_url,
            'test_payload': test_payload,
            **result
        }


# Global webhook service instance
webhook_service = WebhookService()


async def send_real_time_alert(alert_data: Dict):
    """
    Send real-time alert to both WebSocket and webhook subscribers
    
    Args:
        alert_data: Alert information
    """
    # Import here to avoid circular imports
    from app.api.subscription import broadcast_alert
    from app.models.database import SessionLocal
    
    # Send to WebSocket connections
    await broadcast_alert(alert_data)
    
    # Send to webhook subscriptions
    db = SessionLocal()
    try:
        delivery_results = await webhook_service.send_alert_webhook(alert_data, db)
        logger.info(f"Alert sent to {len(delivery_results)} webhook subscribers")
    except Exception as e:
        logger.error(f"Error sending webhook alerts: {str(e)}")
    finally:
        db.close()
