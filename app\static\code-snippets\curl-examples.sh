#!/bin/bash

# Hello Cyril API - cURL Examples
# Complete set of API calls using cURL

# Set your API key here
API_KEY="hc_your_api_key_here"
BASE_URL="https://hellocyril.co.za/api/subscription"

echo "🚀 Hello Cyril API - cURL Examples"
echo "=================================="

# Function to make API calls with error handling
make_api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo ""
    echo "📡 $description"
    echo "   Method: $method"
    echo "   Endpoint: $endpoint"
    
    if [ "$method" = "GET" ]; then
        curl -s -w "\n   Status: %{http_code}\n" \
             -H "X-API-Key: $API_KEY" \
             -H "Content-Type: application/json" \
             "$BASE_URL$endpoint"
    elif [ "$method" = "POST" ]; then
        curl -s -w "\n   Status: %{http_code}\n" \
             -X POST \
             -H "X-API-Key: $API_KEY" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "$BASE_URL$endpoint"
    elif [ "$method" = "DELETE" ]; then
        curl -s -w "\n   Status: %{http_code}\n" \
             -X DELETE \
             -H "X-API-Key: $API_KEY" \
             -H "Content-Type: application/json" \
             "$BASE_URL$endpoint"
    fi
    
    echo "   ✅ Request completed"
}

# 1. Get all reports (basic)
make_api_call "GET" "/reports" "" "Get all reports (default limit: 100)"

# 2. Get crime reports with limit
make_api_call "GET" "/reports?category=crime&limit=10" "" "Get 10 crime reports"

# 3. Get reports with date range
make_api_call "GET" "/reports?start_date=2024-01-01T00:00:00Z&end_date=2024-12-31T23:59:59Z&limit=50" "" "Get reports for 2024"

# 4. Get EMS reports with pagination
make_api_call "GET" "/reports?category=ems&limit=20&offset=0" "" "Get EMS reports (page 1)"

# 5. Get infrastructure reports
make_api_call "GET" "/reports?category=infrastructure&limit=15" "" "Get infrastructure reports"

# 6. Get platform statistics (overall)
make_api_call "GET" "/stats" "" "Get overall platform statistics"

# 7. Get weekly statistics
make_api_call "GET" "/stats?period=weekly" "" "Get weekly statistics"

# 8. Get daily statistics
make_api_call "GET" "/stats?period=daily" "" "Get daily statistics"

# 9. Get monthly statistics
make_api_call "GET" "/stats?period=monthly" "" "Get monthly statistics"

# 10. Get API usage information
make_api_call "GET" "/usage" "" "Get current API usage"

# 11. Subscribe to webhooks (Premium/Enterprise only)
webhook_data='{
    "webhook_url": "https://your-app.com/webhook",
    "alert_types": ["crime", "ems"],
    "geographic_filter": {
        "center": {
            "latitude": -25.7461,
            "longitude": 28.1881
        },
        "radius_km": 10
    }
}'
make_api_call "POST" "/webhooks/subscribe" "$webhook_data" "Subscribe to webhooks with geographic filter"

# 12. Subscribe to all alerts
simple_webhook_data='{
    "webhook_url": "https://your-app.com/webhook",
    "alert_types": ["all"]
}'
make_api_call "POST" "/webhooks/subscribe" "$simple_webhook_data" "Subscribe to all alerts"

# 13. Get webhook status
make_api_call "GET" "/webhooks/status" "" "Get webhook subscription status"

# 14. Test webhook delivery
make_api_call "POST" "/webhooks/test" "" "Test webhook delivery"

# 15. Unsubscribe from webhooks
make_api_call "DELETE" "/webhooks/unsubscribe" "" "Unsubscribe from webhooks"

echo ""
echo "🎉 All API examples completed!"
echo ""
echo "📋 Quick Reference:"
echo "=================="
echo "• Base URL: $BASE_URL"
echo "• Authentication: X-API-Key header"
echo "• Rate Limits: Tier-based (100-2000 req/min)"
echo "• WebSocket: wss://hellocyril.co.za/api/subscription/ws?api_key=YOUR_KEY"
echo ""
echo "📚 Documentation: https://hellocyril.co.za/api-docs"
echo "🔑 Get API Key: https://hellocyril.co.za/subscription"
echo ""

# Advanced examples with error handling and response parsing

echo "🔧 Advanced Examples with Response Parsing"
echo "=========================================="

# Function to parse JSON response
parse_response() {
    local response=$1
    local description=$2
    
    echo ""
    echo "📊 $description"
    
    # Check if jq is available for JSON parsing
    if command -v jq &> /dev/null; then
        echo "   📈 Parsed Response:"
        echo "$response" | jq '.'
    else
        echo "   📄 Raw Response:"
        echo "$response"
        echo "   💡 Install 'jq' for better JSON formatting"
    fi
}

# Get reports and parse response
echo ""
echo "📡 Getting crime reports with response parsing..."
response=$(curl -s -H "X-API-Key: $API_KEY" "$BASE_URL/reports?category=crime&limit=5")
parse_response "$response" "Crime Reports (Last 5)"

# Get usage statistics
echo ""
echo "📡 Getting API usage statistics..."
usage_response=$(curl -s -H "X-API-Key: $API_KEY" "$BASE_URL/usage")
parse_response "$usage_response" "API Usage Statistics"

# Error handling example
echo ""
echo "🚨 Error Handling Example"
echo "========================="
echo "📡 Making request with invalid endpoint..."

error_response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "X-API-Key: $API_KEY" "$BASE_URL/invalid-endpoint")
http_code=$(echo "$error_response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
response_body=$(echo "$error_response" | sed -e 's/HTTPSTATUS:.*//g')

echo "   Status Code: $http_code"
if [ "$http_code" -ne 200 ]; then
    echo "   ❌ Error Response:"
    echo "$response_body"
else
    echo "   ✅ Success Response:"
    echo "$response_body"
fi

echo ""
echo "🎯 Rate Limiting Example"
echo "========================"
echo "📡 Making multiple rapid requests to test rate limiting..."

for i in {1..5}; do
    echo "   Request $i:"
    response=$(curl -s -w "Status: %{http_code}" -H "X-API-Key: $API_KEY" "$BASE_URL/stats")
    echo "   $response"
    sleep 1
done

echo ""
echo "✨ Examples completed! Check the responses above for API behavior."
echo "📖 For more details, visit: https://hellocyril.co.za/api-docs"
