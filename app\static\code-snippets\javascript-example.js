// Hello Cyril API - JavaScript/Node.js Example
// Complete integration example with error handling and WebSocket support

const API_KEY = 'hc_your_api_key_here';
const BASE_URL = 'https://hellocyril.co.za/api/subscription';

/**
 * Hello Cyril API Client Class
 * Provides easy access to all API endpoints
 */
class HelloCyrilAPI {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseURL = BASE_URL;
        this.headers = {
            'X-API-Key': apiKey,
            'Content-Type': 'application/json'
        };
    }

    /**
     * Get reports from the API
     * @param {Object} options - Query parameters
     * @returns {Promise<Object>} API response
     */
    async getReports(options = {}) {
        const params = new URLSearchParams(options);
        
        try {
            const response = await fetch(`${this.baseURL}/reports?${params}`, {
                headers: this.headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching reports:', error);
            throw error;
        }
    }

    /**
     * Get platform statistics
     * @param {string} period - Time period (overall, daily, weekly, monthly)
     * @returns {Promise<Object>} Statistics data
     */
    async getStats(period = 'overall') {
        try {
            const response = await fetch(`${this.baseURL}/stats?period=${period}`, {
                headers: this.headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching stats:', error);
            throw error;
        }
    }

    /**
     * Get current API usage
     * @returns {Promise<Object>} Usage statistics
     */
    async getUsage() {
        try {
            const response = await fetch(`${this.baseURL}/usage`, {
                headers: this.headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching usage:', error);
            throw error;
        }
    }

    /**
     * Subscribe to webhooks (Premium/Enterprise only)
     * @param {string} webhookUrl - Your webhook endpoint URL
     * @param {Array} alertTypes - Types of alerts to receive
     * @param {Object} geoFilter - Geographic filter options
     * @returns {Promise<Object>} Subscription result
     */
    async subscribeWebhook(webhookUrl, alertTypes = ['all'], geoFilter = null) {
        const data = {
            webhook_url: webhookUrl,
            alert_types: alertTypes
        };

        if (geoFilter) {
            data.geographic_filter = geoFilter;
        }

        try {
            const response = await fetch(`${this.baseURL}/webhooks/subscribe`, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error subscribing to webhook:', error);
            throw error;
        }
    }

    /**
     * Test webhook delivery
     * @returns {Promise<Object>} Test result
     */
    async testWebhook() {
        try {
            const response = await fetch(`${this.baseURL}/webhooks/test`, {
                method: 'POST',
                headers: this.headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error testing webhook:', error);
            throw error;
        }
    }

    /**
     * Connect to WebSocket for real-time alerts
     * @param {Function} onAlert - Callback for new alerts
     * @param {Function} onError - Error callback
     * @returns {WebSocket} WebSocket connection
     */
    connectWebSocket(onAlert, onError) {
        const wsUrl = `wss://hellocyril.co.za/api/subscription/ws?api_key=${this.apiKey}`;
        const ws = new WebSocket(wsUrl);

        ws.onopen = (event) => {
            console.log('✅ Connected to Hello Cyril WebSocket');
            
            // Send ping every 30 seconds to keep connection alive
            const pingInterval = setInterval(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({ type: 'ping' }));
                } else {
                    clearInterval(pingInterval);
                }
            }, 30000);
        };

        ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                console.log('📨 Received:', message);

                switch (message.type) {
                    case 'connection':
                        console.log('🔗 Connection established:', message.message);
                        break;
                    case 'alert':
                        console.log('🚨 New alert received');
                        if (onAlert) onAlert(message.data);
                        break;
                    case 'pong':
                        console.log('🏓 Pong received');
                        break;
                    default:
                        console.log('📋 Unknown message type:', message.type);
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        ws.onclose = (event) => {
            console.log('❌ WebSocket connection closed');
            
            // Reconnect after 5 seconds if not manually closed
            if (event.code !== 1000) {
                console.log('🔄 Reconnecting in 5 seconds...');
                setTimeout(() => {
                    this.connectWebSocket(onAlert, onError);
                }, 5000);
            }
        };

        ws.onerror = (error) => {
            console.error('❌ WebSocket error:', error);
            if (onError) onError(error);
        };

        return ws;
    }
}

// Usage Examples
async function main() {
    // Initialize API client
    const api = new HelloCyrilAPI(API_KEY);

    try {
        // Example 1: Get recent crime reports
        console.log('📊 Fetching crime reports...');
        const crimeReports = await api.getReports({
            category: 'crime',
            limit: 10
        });
        console.log(`Found ${crimeReports.reports.length} crime reports`);

        // Example 2: Get platform statistics
        console.log('📈 Fetching platform stats...');
        const stats = await api.getStats('weekly');
        console.log('Platform stats:', stats);

        // Example 3: Check API usage
        console.log('📋 Checking API usage...');
        const usage = await api.getUsage();
        console.log(`Usage: ${usage.current_month_requests}/${usage.monthly_request_limit} requests`);

        // Example 4: Subscribe to webhooks (Premium/Enterprise only)
        // const webhookResult = await api.subscribeWebhook(
        //     'https://your-app.com/webhook',
        //     ['crime', 'ems'],
        //     {
        //         center: { latitude: -25.7461, longitude: 28.1881 },
        //         radius_km: 10
        //     }
        // );

        // Example 5: Connect to real-time WebSocket
        const ws = api.connectWebSocket(
            (alertData) => {
                console.log('🚨 New alert:', alertData);
                // Handle the alert in your application
                handleNewAlert(alertData);
            },
            (error) => {
                console.error('WebSocket error:', error);
            }
        );

    } catch (error) {
        console.error('❌ API Error:', error.message);
    }
}

/**
 * Handle new alerts from WebSocket
 * @param {Object} alertData - Alert information
 */
function handleNewAlert(alertData) {
    // Example alert handling
    const alertElement = document.createElement('div');
    alertElement.className = 'alert alert-warning';
    alertElement.innerHTML = `
        <strong>New ${alertData.category} Alert!</strong><br>
        ${alertData.description}<br>
        <small>Location: ${alertData.location.latitude}, ${alertData.location.longitude}</small>
    `;
    
    // Add to page or send notification
    document.body.appendChild(alertElement);
    
    // Remove after 10 seconds
    setTimeout(() => {
        alertElement.remove();
    }, 10000);
}

// Start the application
// main();
