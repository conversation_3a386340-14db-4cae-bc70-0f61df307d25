#!/usr/bin/env python3
"""
Hello Cyril API - Python Example
Complete integration example with error handling and WebSocket support
"""

import requests
import websocket
import json
import threading
import time
import logging
from typing import Dict, List, Optional, Callable

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

API_KEY = 'hc_your_api_key_here'
BASE_URL = 'https://hellocyril.co.za/api/subscription'


class HelloCyrilAPI:
    """
    Hello Cyril API Client
    Provides easy access to all API endpoints with error handling
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = BASE_URL
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_reports(self, category: Optional[str] = None, 
                   start_date: Optional[str] = None,
                   end_date: Optional[str] = None,
                   limit: int = 100,
                   offset: int = 0) -> Dict:
        """
        Get reports from the API
        
        Args:
            category: Filter by category (crime, ems, infrastructure)
            start_date: Start date in ISO format
            end_date: End date in ISO format
            limit: Number of results (max 1000)
            offset: Pagination offset
            
        Returns:
            Dict containing reports and metadata
        """
        params = {
            'limit': limit,
            'offset': offset
        }
        
        if category:
            params['category'] = category
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
        
        try:
            response = self.session.get(f'{self.base_url}/reports', params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching reports: {e}")
            raise
    
    def get_stats(self, period: str = 'overall') -> Dict:
        """
        Get platform statistics
        
        Args:
            period: Time period (overall, daily, weekly, monthly)
            
        Returns:
            Dict containing statistics
        """
        try:
            response = self.session.get(
                f'{self.base_url}/stats',
                params={'period': period}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching stats: {e}")
            raise
    
    def get_usage(self) -> Dict:
        """
        Get current API usage statistics
        
        Returns:
            Dict containing usage information
        """
        try:
            response = self.session.get(f'{self.base_url}/usage')
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching usage: {e}")
            raise
    
    def subscribe_webhook(self, webhook_url: str, 
                         alert_types: List[str] = None,
                         geographic_filter: Optional[Dict] = None) -> Dict:
        """
        Subscribe to webhook notifications (Premium/Enterprise only)
        
        Args:
            webhook_url: Your webhook endpoint URL
            alert_types: List of alert types to receive
            geographic_filter: Geographic filter options
            
        Returns:
            Dict containing subscription result
        """
        if alert_types is None:
            alert_types = ['all']
        
        data = {
            'webhook_url': webhook_url,
            'alert_types': alert_types
        }
        
        if geographic_filter:
            data['geographic_filter'] = geographic_filter
        
        try:
            response = self.session.post(
                f'{self.base_url}/webhooks/subscribe',
                json=data
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error subscribing to webhook: {e}")
            raise
    
    def get_webhook_status(self) -> Dict:
        """
        Get webhook subscription status
        
        Returns:
            Dict containing webhook status
        """
        try:
            response = self.session.get(f'{self.base_url}/webhooks/status')
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting webhook status: {e}")
            raise
    
    def test_webhook(self) -> Dict:
        """
        Test webhook delivery
        
        Returns:
            Dict containing test result
        """
        try:
            response = self.session.post(f'{self.base_url}/webhooks/test')
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error testing webhook: {e}")
            raise
    
    def unsubscribe_webhook(self) -> Dict:
        """
        Unsubscribe from webhook notifications
        
        Returns:
            Dict containing unsubscribe result
        """
        try:
            response = self.session.delete(f'{self.base_url}/webhooks/unsubscribe')
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error unsubscribing from webhook: {e}")
            raise


class HelloCyrilWebSocket:
    """
    WebSocket client for real-time Hello Cyril alerts
    """
    
    def __init__(self, api_key: str, on_alert: Optional[Callable] = None):
        self.api_key = api_key
        self.on_alert = on_alert
        self.ws = None
        self.running = False
        
    def on_message(self, ws, message):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(message)
            logger.info(f"📨 Received: {data.get('type', 'unknown')}")
            
            message_type = data.get('type')
            
            if message_type == 'connection':
                logger.info(f"🔗 {data.get('message', 'Connected')}")
            elif message_type == 'alert':
                logger.info("🚨 New alert received")
                if self.on_alert:
                    self.on_alert(data.get('data'))
                else:
                    self.handle_alert(data.get('data'))
            elif message_type == 'pong':
                logger.debug("🏓 Pong received")
            else:
                logger.info(f"📋 Unknown message type: {message_type}")
                
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing WebSocket message: {e}")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
    
    def on_error(self, ws, error):
        """Handle WebSocket errors"""
        logger.error(f"❌ WebSocket error: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket connection close"""
        logger.info("❌ WebSocket connection closed")
        
        # Reconnect if not manually stopped
        if self.running:
            logger.info("🔄 Reconnecting in 5 seconds...")
            time.sleep(5)
            self.connect()
    
    def on_open(self, ws):
        """Handle WebSocket connection open"""
        logger.info("✅ Connected to Hello Cyril WebSocket")
        
        # Start ping thread to keep connection alive
        def ping_thread():
            while self.running and ws.sock and ws.sock.connected:
                try:
                    time.sleep(30)
                    if ws.sock and ws.sock.connected:
                        ws.send(json.dumps({'type': 'ping'}))
                        logger.debug("🏓 Ping sent")
                except Exception as e:
                    logger.error(f"Error sending ping: {e}")
                    break
        
        threading.Thread(target=ping_thread, daemon=True).start()
    
    def handle_alert(self, alert_data: Dict):
        """
        Default alert handler
        Override this method or provide on_alert callback
        """
        logger.info(f"🚨 New {alert_data.get('category', 'unknown')} alert:")
        logger.info(f"   Description: {alert_data.get('description', 'N/A')}")
        logger.info(f"   Location: {alert_data.get('location', {})}")
        logger.info(f"   Timestamp: {alert_data.get('timestamp', 'N/A')}")
    
    def connect(self):
        """Connect to WebSocket"""
        ws_url = f"wss://hellocyril.co.za/api/subscription/ws?api_key={self.api_key}"
        
        self.running = True
        self.ws = websocket.WebSocketApp(
            ws_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        
        # Run forever (blocking)
        self.ws.run_forever()
    
    def disconnect(self):
        """Disconnect from WebSocket"""
        self.running = False
        if self.ws:
            self.ws.close()


def main():
    """Example usage of the Hello Cyril API"""
    
    # Initialize API client
    api = HelloCyrilAPI(API_KEY)
    
    try:
        # Example 1: Get recent crime reports
        logger.info("📊 Fetching crime reports...")
        crime_reports = api.get_reports(category='crime', limit=10)
        logger.info(f"Found {len(crime_reports['reports'])} crime reports")
        
        # Example 2: Get platform statistics
        logger.info("📈 Fetching platform stats...")
        stats = api.get_stats(period='weekly')
        logger.info(f"Total reports: {stats['stats']['total_reports']}")
        
        # Example 3: Check API usage
        logger.info("📋 Checking API usage...")
        usage = api.get_usage()
        logger.info(f"Usage: {usage['current_month_requests']}/{usage['monthly_request_limit']} requests")
        logger.info(f"Usage percentage: {usage['usage_percentage']}%")
        
        # Example 4: Subscribe to webhooks (Premium/Enterprise only)
        # webhook_result = api.subscribe_webhook(
        #     webhook_url='https://your-app.com/webhook',
        #     alert_types=['crime', 'ems'],
        #     geographic_filter={
        #         'center': {'latitude': -25.7461, 'longitude': 28.1881},
        #         'radius_km': 10
        #     }
        # )
        # logger.info(f"Webhook subscription: {webhook_result}")
        
        # Example 5: Test webhook delivery
        # test_result = api.test_webhook()
        # logger.info(f"Webhook test: {test_result}")
        
    except Exception as e:
        logger.error(f"❌ API Error: {e}")


def websocket_example():
    """Example WebSocket usage"""
    
    def custom_alert_handler(alert_data):
        """Custom alert handler"""
        print(f"🚨 CUSTOM ALERT: {alert_data.get('category', 'unknown').upper()}")
        print(f"   📍 Location: {alert_data.get('location', {})}")
        print(f"   📝 Description: {alert_data.get('description', 'N/A')}")
        
        # Add your custom logic here
        # - Send notifications
        # - Update database
        # - Trigger other systems
    
    # Connect to WebSocket with custom handler
    ws_client = HelloCyrilWebSocket(API_KEY, on_alert=custom_alert_handler)
    
    try:
        logger.info("🔌 Connecting to WebSocket...")
        ws_client.connect()  # This blocks until disconnected
    except KeyboardInterrupt:
        logger.info("👋 Disconnecting...")
        ws_client.disconnect()
    except Exception as e:
        logger.error(f"WebSocket error: {e}")


if __name__ == "__main__":
    # Run API examples
    main()
    
    # Uncomment to test WebSocket connection
    # websocket_example()
