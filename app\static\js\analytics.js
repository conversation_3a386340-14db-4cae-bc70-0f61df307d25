/**
 * Hello Cyril Analytics Tracking
 * Tracks user interactions for analytics purposes
 */

class HelloCyrilAnalytics {
    constructor() {
        this.baseUrl = '/api/analytics';
        this.init();
    }

    init() {
        // Track page visits automatically
        this.trackPageVisit();

        // Set up WhatsApp link tracking
        this.setupWhatsAppTracking();

        // Set up donation tracking
        this.setupDonationTracking();
    }

    async trackPageVisit() {
        try {
            const data = {
                page_url: window.location.pathname + window.location.search,
                page_title: document.title,
                referrer: document.referrer
            };

            await fetch(`${this.baseUrl}/track/page-visit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
        } catch (error) {
            // Silently fail - don't disrupt user experience
            console.debug('Analytics tracking failed:', error);
        }
    }

    setupWhatsAppTracking() {
        // Track all WhatsApp links
        document.addEventListener('click', (event) => {
            const target = event.target.closest('a');
            if (!target) return;

            const href = target.href;
            if (href && (href.includes('wa.me') || href.includes('whatsapp.com') || href.includes('api.whatsapp.com'))) {
                this.trackWhatsAppClick(target);
            }
        });
    }

    async trackWhatsAppClick(element) {
        try {
            // Determine click source based on element location
            let clickSource = 'unknown';

            if (element.closest('nav') || element.closest('.navbar')) {
                clickSource = 'navbar';
            } else if (element.closest('header')) {
                clickSource = 'header';
            } else if (element.closest('footer')) {
                clickSource = 'footer';
            } else if (element.closest('.hero') || element.closest('.banner')) {
                clickSource = 'hero';
            } else if (element.closest('.contact') || element.closest('.contact-section')) {
                clickSource = 'contact-section';
            } else {
                clickSource = 'content';
            }

            // Extract WhatsApp number from href
            const href = element.href;
            let whatsappNumber = null;
            const numberMatch = href.match(/(\+?\d{10,15})/);
            if (numberMatch) {
                whatsappNumber = numberMatch[1];
            }

            const data = {
                click_source: clickSource,
                whatsapp_number: whatsappNumber,
                referrer_page: window.location.pathname + window.location.search
            };

            await fetch(`${this.baseUrl}/track/whatsapp-click`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
        } catch (error) {
            console.debug('WhatsApp click tracking failed:', error);
        }
    }

    setupDonationTracking() {
        // Track donation button clicks and form submissions
        document.addEventListener('click', (event) => {
            const target = event.target.closest('button, a');
            if (!target) return;

            // Check if it's a donation-related button
            const text = target.textContent.toLowerCase();
            const classes = target.className.toLowerCase();
            const id = target.id.toLowerCase();

            if (text.includes('donate') || text.includes('donation') ||
                classes.includes('donate') || classes.includes('donation') ||
                id.includes('donate') || id.includes('donation')) {

                this.trackDonationIntent(target);
            }
        });

        // Track form submissions that might be donations
        document.addEventListener('submit', (event) => {
            const form = event.target;
            if (form.action && (form.action.includes('donate') || form.action.includes('payment'))) {
                this.trackDonationFormSubmission(form);
            }
        });
    }

    async trackDonationIntent(element = null) {
        try {
            // This tracks when someone clicks a donation button
            // The actual donation tracking with amount happens in the payment flow
            const data = {
                amount: 0, // Intent only, no amount yet
                payment_method: 'unknown',
                payment_status: 'intent',
                transaction_id: null
            };

            await fetch(`${this.baseUrl}/track/donation`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
        } catch (error) {
            console.debug('Donation intent tracking failed:', error);
        }
    }

    async trackDonationFormSubmission(form) {
        try {
            // Extract amount from form if available
            const amountInput = form.querySelector('input[name*="amount"], input[id*="amount"]');
            const amount = amountInput ? parseFloat(amountInput.value) || 0 : 0;

            const data = {
                amount: amount,
                payment_method: 'form_submission',
                payment_status: 'initiated',
                transaction_id: null
            };

            const response = await fetch(`${this.baseUrl}/track/donation`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            // Store the tracking ID for later updates
            if (result.id) {
                sessionStorage.setItem('donation_tracking_id', result.id);
            }
        } catch (error) {
            console.debug('Donation form tracking failed:', error);
        }
    }

    // Public method to track successful donations (call from payment success callbacks)
    async trackDonationSuccess(amount, paymentMethod, transactionId, paymentProviderId) {
        try {
            const data = {
                amount: amount,
                payment_method: paymentMethod,
                payment_status: 'completed',
                transaction_id: transactionId,
                payment_provider_id: paymentProviderId
            };

            const response = await fetch(`${this.baseUrl}/track/donation`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            return await response.json();
        } catch (error) {
            console.debug('Donation success tracking failed:', error);
        }
    }

    // Public method to update existing donation tracking
    async updateDonationStatus(trackingId, status, paymentProviderId, notes) {
        try {
            const data = {
                payment_status: status,
                payment_provider_id: paymentProviderId,
                notes: notes
            };

            await fetch(`${this.baseUrl}/track/donation/${trackingId}/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
        } catch (error) {
            console.debug('Donation status update failed:', error);
        }
    }
}

// Initialize analytics when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.helloCyrilAnalytics = new HelloCyrilAnalytics();
});

// Export for manual usage
window.HelloCyrilAnalytics = HelloCyrilAnalytics;
