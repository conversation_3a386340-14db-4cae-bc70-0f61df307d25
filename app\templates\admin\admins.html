{% extends "admin/base.html" %}

{% block title %}Admin Users - Admin Panel{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users-cog me-2"></i>
                Admin Users
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                <i class="fas fa-plus me-2"></i>
                Add Admin
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="adminsTable">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="adminsTableBody">
                            <tr>
                                <td colspan="6" class="text-center">Loading...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Admin Modal -->
<div class="modal fade" id="addAdminModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    Add New Admin
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addAdminForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="addUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="addUsername" required>
                    </div>
                    <div class="mb-3">
                        <label for="addEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="addEmail" required>
                    </div>
                    <div class="mb-3">
                        <label for="addPassword" class="form-label">Password</label>
                        <input type="password" class="form-control" id="addPassword" required minlength="6">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Create Admin
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Admin Modal -->
<div class="modal fade" id="editAdminModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>
                    Edit Admin
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editAdminForm">
                <div class="modal-body">
                    <input type="hidden" id="editAdminId">
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="editUsername" required>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" required>
                    </div>
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">New Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="editPassword" minlength="6">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsActive">
                            <label class="form-check-label" for="editIsActive">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Update Admin
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let admins = [];

    document.addEventListener('DOMContentLoaded', function() {
        loadAdmins();
        
        // Add admin form handler
        document.getElementById('addAdminForm').addEventListener('submit', handleAddAdmin);
        
        // Edit admin form handler
        document.getElementById('editAdminForm').addEventListener('submit', handleEditAdmin);
    });

    async function loadAdmins() {
        try {
            const response = await makeAuthenticatedRequest('/api/admin/admins');
            if (response.ok) {
                admins = await response.json();
                renderAdminsTable();
            } else {
                showAlert('Error loading admins', 'danger');
            }
        } catch (error) {
            showAlert('Network error loading admins', 'danger');
        }
    }

    function renderAdminsTable() {
        const tbody = document.getElementById('adminsTableBody');
        
        if (admins.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">No admins found</td></tr>';
            return;
        }

        tbody.innerHTML = admins.map(admin => `
            <tr>
                <td>${admin.username}</td>
                <td>${admin.email}</td>
                <td>
                    <span class="badge ${admin.is_active ? 'bg-success' : 'bg-danger'}">
                        ${admin.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${new Date(admin.created_at).toLocaleDateString()}</td>
                <td>${admin.last_login ? new Date(admin.last_login).toLocaleDateString() : 'Never'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editAdmin('${admin.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${admin.is_active ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="deactivateAdmin('${admin.id}', '${admin.username}')">
                            <i class="fas fa-ban"></i>
                        </button>
                    ` : ''}
                </td>
            </tr>
        `).join('');
    }

    async function handleAddAdmin(e) {
        e.preventDefault();
        
        const formData = {
            username: document.getElementById('addUsername').value,
            email: document.getElementById('addEmail').value,
            password: document.getElementById('addPassword').value
        };

        try {
            const response = await makeAuthenticatedRequest('/api/admin/admins', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                showAlert('Admin created successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addAdminModal')).hide();
                document.getElementById('addAdminForm').reset();
                loadAdmins();
            } else {
                const error = await response.json();
                showAlert(error.detail || 'Error creating admin', 'danger');
            }
        } catch (error) {
            showAlert('Network error creating admin', 'danger');
        }
    }

    function editAdmin(adminId) {
        const admin = admins.find(a => a.id === adminId);
        if (!admin) return;

        document.getElementById('editAdminId').value = admin.id;
        document.getElementById('editUsername').value = admin.username;
        document.getElementById('editEmail').value = admin.email;
        document.getElementById('editPassword').value = '';
        document.getElementById('editIsActive').checked = admin.is_active;

        new bootstrap.Modal(document.getElementById('editAdminModal')).show();
    }

    async function handleEditAdmin(e) {
        e.preventDefault();
        
        const adminId = document.getElementById('editAdminId').value;
        const formData = {
            username: document.getElementById('editUsername').value,
            email: document.getElementById('editEmail').value,
            is_active: document.getElementById('editIsActive').checked
        };

        const password = document.getElementById('editPassword').value;
        if (password) {
            formData.password = password;
        }

        try {
            const response = await makeAuthenticatedRequest(`/api/admin/admins/${adminId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });

            if (response.ok) {
                showAlert('Admin updated successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('editAdminModal')).hide();
                loadAdmins();
            } else {
                const error = await response.json();
                showAlert(error.detail || 'Error updating admin', 'danger');
            }
        } catch (error) {
            showAlert('Network error updating admin', 'danger');
        }
    }

    async function deactivateAdmin(adminId, username) {
        if (!confirm(`Are you sure you want to deactivate admin "${username}"?`)) {
            return;
        }

        try {
            const response = await makeAuthenticatedRequest(`/api/admin/admins/${adminId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                showAlert('Admin deactivated successfully', 'success');
                loadAdmins();
            } else {
                const error = await response.json();
                showAlert(error.detail || 'Error deactivating admin', 'danger');
            }
        } catch (error) {
            showAlert('Network error deactivating admin', 'danger');
        }
    }

    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Insert at the top of the content
        const content = document.querySelector('.container-fluid');
        content.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = content.querySelector('.alert');
            if (alert) {
                bootstrap.Alert.getOrCreateInstance(alert).close();
            }
        }, 5000);
    }
</script>
{% endblock %}
