{% extends "admin/base.html" %}

{% block title %}Analytics - Admin Panel{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-chart-line me-2"></i>
            Website Analytics
        </h1>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Page Visits (30 days)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-visits">
                            Loading...
                        </div>
                        <small class="text-muted" id="unique-visitors"></small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            WhatsApp Clicks (30 days)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-whatsapp-clicks">
                            Loading...
                        </div>
                        <small class="text-muted" id="unique-clickers"></small>
                    </div>
                    <div class="col-auto">
                        <i class="fab fa-whatsapp fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Donations (30 days)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-donations">
                            Loading...
                        </div>
                        <small class="text-muted" id="donation-amount"></small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-heart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Conversion Rate
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="conversion-rate">
                            Loading...
                        </div>
                        <small class="text-muted">Visits to WhatsApp clicks</small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Navigation Tabs -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header" style="background: #ee4d4d; color: white;">
                <ul class="nav nav-tabs card-header-tabs" id="analyticsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="visits-tab" data-bs-toggle="tab" data-bs-target="#visits" type="button" role="tab">
                            <i class="fas fa-eye me-2"></i>Page Visits
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="whatsapp-tab" data-bs-toggle="tab" data-bs-target="#whatsapp" type="button" role="tab">
                            <i class="fab fa-whatsapp me-2"></i>WhatsApp Clicks
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="donations-tab" data-bs-toggle="tab" data-bs-target="#donations" type="button" role="tab">
                            <i class="fas fa-heart me-2"></i>Donations
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="analyticsTabContent">
                    <!-- Page Visits Tab -->
                    <div class="tab-pane fade show active" id="visits" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="m-0">Recent Page Visits</h6>
                            <div>
                                <select class="form-select form-select-sm" id="visits-filter">
                                    <option value="7">Last 7 days</option>
                                    <option value="30" selected>Last 30 days</option>
                                    <option value="90">Last 90 days</option>
                                    <option value="">All time</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="visitsTable">
                                <thead>
                                    <tr>
                                        <th>IP Address</th>
                                        <th>Page</th>
                                        <th>Title</th>
                                        <th>Referrer</th>
                                        <th>Visit Time</th>
                                        <th>User Agent</th>
                                    </tr>
                                </thead>
                                <tbody id="visitsTableBody">
                                    <tr><td colspan="6" class="text-center">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- WhatsApp Clicks Tab -->
                    <div class="tab-pane fade" id="whatsapp" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="m-0">WhatsApp Link Clicks</h6>
                            <div>
                                <select class="form-select form-select-sm" id="whatsapp-filter">
                                    <option value="7">Last 7 days</option>
                                    <option value="30" selected>Last 30 days</option>
                                    <option value="90">Last 90 days</option>
                                    <option value="">All time</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="whatsappTable">
                                <thead>
                                    <tr>
                                        <th>IP Address</th>
                                        <th>Click Source</th>
                                        <th>WhatsApp Number</th>
                                        <th>Referrer Page</th>
                                        <th>Click Time</th>
                                        <th>User Agent</th>
                                    </tr>
                                </thead>
                                <tbody id="whatsappTableBody">
                                    <tr><td colspan="6" class="text-center">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Donations Tab -->
                    <div class="tab-pane fade" id="donations" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="m-0">Donation Tracking</h6>
                            <div>
                                <select class="form-select form-select-sm me-2" id="donations-status-filter">
                                    <option value="">All statuses</option>
                                    <option value="completed">Completed</option>
                                    <option value="initiated">Initiated</option>
                                    <option value="failed">Failed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                                <select class="form-select form-select-sm" id="donations-filter">
                                    <option value="7">Last 7 days</option>
                                    <option value="30" selected>Last 30 days</option>
                                    <option value="90">Last 90 days</option>
                                    <option value="">All time</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="donationsTable">
                                <thead>
                                    <tr>
                                        <th>IP Address</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Transaction ID</th>
                                        <th>Donation Time</th>
                                        <th>Completion Time</th>
                                    </tr>
                                </thead>
                                <tbody id="donationsTableBody">
                                    <tr><td colspan="7" class="text-center">Loading...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .text-xs {
        font-size: 0.7rem;
    }
    .text-gray-800 {
        color: #5a5c69 !important;
    }
    .text-gray-300 {
        color: #dddfeb !important;
    }
    .nav-tabs .nav-link {
        color: white !important;
        border: none !important;
    }
    .nav-tabs .nav-link.active {
        background-color: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let currentData = {
        visits: [],
        whatsapp: [],
        donations: []
    };

    document.addEventListener('DOMContentLoaded', function() {
        loadAnalyticsSummary();
        loadPageVisits();
        
        // Tab change handlers
        document.getElementById('whatsapp-tab').addEventListener('click', () => loadWhatsAppClicks());
        document.getElementById('donations-tab').addEventListener('click', () => loadDonations());
        
        // Filter change handlers
        document.getElementById('visits-filter').addEventListener('change', loadPageVisits);
        document.getElementById('whatsapp-filter').addEventListener('change', loadWhatsAppClicks);
        document.getElementById('donations-filter').addEventListener('change', loadDonations);
        document.getElementById('donations-status-filter').addEventListener('change', loadDonations);
    });

    async function loadAnalyticsSummary() {
        try {
            const response = await makeAuthenticatedRequest('/api/analytics/admin/analytics-summary?days=30');
            if (response.ok) {
                const data = await response.json();
                
                document.getElementById('total-visits').textContent = data.page_visits.total;
                document.getElementById('unique-visitors').textContent = `${data.page_visits.unique_visitors} unique visitors`;
                
                document.getElementById('total-whatsapp-clicks').textContent = data.whatsapp_clicks.total;
                document.getElementById('unique-clickers').textContent = `${data.whatsapp_clicks.unique_clickers} unique clickers`;
                
                document.getElementById('total-donations').textContent = data.donations.total_count;
                document.getElementById('donation-amount').textContent = `R ${data.donations.total_amount.toFixed(2)}`;
                
                // Calculate conversion rate
                const conversionRate = data.page_visits.total > 0 ? 
                    ((data.whatsapp_clicks.total / data.page_visits.total) * 100).toFixed(1) : 0;
                document.getElementById('conversion-rate').textContent = `${conversionRate}%`;
            }
        } catch (error) {
            console.error('Error loading analytics summary:', error);
        }
    }

    async function loadPageVisits() {
        const days = document.getElementById('visits-filter').value;
        const url = `/api/analytics/admin/page-visits?limit=100${days ? `&days=${days}` : ''}`;
        
        try {
            const response = await makeAuthenticatedRequest(url);
            if (response.ok) {
                const data = await response.json();
                currentData.visits = data.visits;
                renderVisitsTable();
            }
        } catch (error) {
            console.error('Error loading page visits:', error);
        }
    }

    async function loadWhatsAppClicks() {
        const days = document.getElementById('whatsapp-filter').value;
        const url = `/api/analytics/admin/whatsapp-clicks?limit=100${days ? `&days=${days}` : ''}`;
        
        try {
            const response = await makeAuthenticatedRequest(url);
            if (response.ok) {
                const data = await response.json();
                currentData.whatsapp = data.clicks;
                renderWhatsAppTable();
            }
        } catch (error) {
            console.error('Error loading WhatsApp clicks:', error);
        }
    }

    async function loadDonations() {
        const days = document.getElementById('donations-filter').value;
        const status = document.getElementById('donations-status-filter').value;
        const url = `/api/analytics/admin/donations?limit=100${days ? `&days=${days}` : ''}${status ? `&status=${status}` : ''}`;
        
        try {
            const response = await makeAuthenticatedRequest(url);
            if (response.ok) {
                const data = await response.json();
                currentData.donations = data.donations;
                renderDonationsTable();
            }
        } catch (error) {
            console.error('Error loading donations:', error);
        }
    }

    function renderVisitsTable() {
        const tbody = document.getElementById('visitsTableBody');
        
        if (currentData.visits.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">No visits found</td></tr>';
            return;
        }

        tbody.innerHTML = currentData.visits.map(visit => `
            <tr>
                <td><code>${visit.ip_address}</code></td>
                <td>${visit.page_url}</td>
                <td>${visit.page_title || 'N/A'}</td>
                <td>${visit.referrer ? `<small>${visit.referrer.substring(0, 50)}${visit.referrer.length > 50 ? '...' : ''}</small>` : 'Direct'}</td>
                <td>${new Date(visit.visit_time).toLocaleString()}</td>
                <td><small>${visit.user_agent ? visit.user_agent.substring(0, 50) + '...' : 'Unknown'}</small></td>
            </tr>
        `).join('');
    }

    function renderWhatsAppTable() {
        const tbody = document.getElementById('whatsappTableBody');
        
        if (currentData.whatsapp.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">No WhatsApp clicks found</td></tr>';
            return;
        }

        tbody.innerHTML = currentData.whatsapp.map(click => `
            <tr>
                <td><code>${click.ip_address}</code></td>
                <td><span class="badge bg-primary">${click.click_source}</span></td>
                <td>${click.whatsapp_number || 'N/A'}</td>
                <td>${click.referrer_page || 'N/A'}</td>
                <td>${new Date(click.click_time).toLocaleString()}</td>
                <td><small>${click.user_agent ? click.user_agent.substring(0, 50) + '...' : 'Unknown'}</small></td>
            </tr>
        `).join('');
    }

    function renderDonationsTable() {
        const tbody = document.getElementById('donationsTableBody');
        
        if (currentData.donations.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">No donations found</td></tr>';
            return;
        }

        tbody.innerHTML = currentData.donations.map(donation => `
            <tr>
                <td><code>${donation.ip_address}</code></td>
                <td><strong>R ${donation.amount.toFixed(2)}</strong></td>
                <td><span class="badge bg-info">${donation.payment_method}</span></td>
                <td><span class="badge ${getStatusBadgeClass(donation.payment_status)}">${donation.payment_status}</span></td>
                <td><small>${donation.transaction_id || 'N/A'}</small></td>
                <td>${new Date(donation.donation_time).toLocaleString()}</td>
                <td>${donation.completion_time ? new Date(donation.completion_time).toLocaleString() : 'N/A'}</td>
            </tr>
        `).join('');
    }

    function getStatusBadgeClass(status) {
        switch(status) {
            case 'completed': return 'bg-success';
            case 'failed': return 'bg-danger';
            case 'cancelled': return 'bg-warning';
            case 'initiated': return 'bg-secondary';
            default: return 'bg-secondary';
        }
    }
</script>
{% endblock %}
