<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Panel - Hello Cyril{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', path='img/lowhellocyril.jpg') }}" type="image/jpeg">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Dosis:300,400,500,600,700">

    <style>
        /* Import main website styles */
        @import "https://fonts.googleapis.com/css?family=Dosis:300,400,500,600,700";

        body {
            background: #f9f9f9;
            font-family: "Dosis", helvetica, arial, tahoma, verdana;
            line-height: 20px;
            font-size: 14px;
            color: #726f77;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: "Dosis", arial, tahoma, verdana;
            font-weight: 500;
        }

        .sidebar {
            min-height: 100vh;
            background: #2b2e48;
            box-shadow: 0 3px 3px rgba(0, 0, 0, 0.05);
        }

        .sidebar .nav-link {
            color: #f7aaaa;
            padding: 0.75rem 1rem;
            font-family: "Dosis";
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            color: #fff;
            background-color: #ee4d4d;
            border-radius: 5px;
            margin: 2px 8px;
        }

        .sidebar .nav-link.active {
            color: #fff;
            background-color: #ee4d4d;
            border-radius: 5px;
            margin: 2px 8px;
        }

        .main-content {
            margin-left: 0;
        }

        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }

        .navbar-brand {
            font-weight: bold;
            font-family: "Dosis";
        }

        .card {
            background: #fff;
            box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            border: none;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: #ee4d4d;
            border: 2px solid #ee4d4d;
            color: #fff;
            font-family: "Dosis";
            font-weight: 500;
            text-transform: uppercase;
            font-size: 12px;
            border-radius: 5px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 0 #f27c7c;
        }

        .btn-primary:hover {
            background: #f27c7c;
            border-color: #f27c7c;
            box-shadow: none;
            transform: translate(2px, 2px);
        }

        .btn-outline-primary {
            border: 2px solid #ee4d4d;
            color: #ee4d4d;
            background: transparent;
            font-family: "Dosis";
            font-weight: 500;
            text-transform: uppercase;
            font-size: 12px;
            border-radius: 5px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 0 #f27c7c;
        }

        .btn-outline-primary:hover {
            background: #ee4d4d;
            border-color: #ee4d4d;
            color: #fff;
            box-shadow: none;
            transform: translate(2px, 2px);
        }

        .btn-success {
            background: #28a745;
            border: 2px solid #28a745;
            color: #fff;
            font-family: "Dosis";
            font-weight: 500;
            text-transform: uppercase;
            font-size: 12px;
            border-radius: 5px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 0 #34ce57;
        }

        .btn-success:hover {
            background: #34ce57;
            border-color: #34ce57;
            box-shadow: none;
            transform: translate(2px, 2px);
        }

        .btn-danger {
            background: #dc3545;
            border: 2px solid #dc3545;
            color: #fff;
            font-family: "Dosis";
            font-weight: 500;
            text-transform: uppercase;
            font-size: 12px;
            border-radius: 5px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 0 #e4606d;
        }

        .btn-danger:hover {
            background: #e4606d;
            border-color: #e4606d;
            box-shadow: none;
            transform: translate(2px, 2px);
        }

        .admin-header {
            background: #2b2e48;
            color: #ee4d4d;
            padding: 10px 0;
            box-shadow: 0 3px 3px rgba(0, 0, 0, 0.05);
        }

        .admin-logo {
            color: #ee4d4d;
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 22px;
            font-weight: 500;
            text-decoration: none;
        }

        .admin-logo > span {
            color: #f7aaaa;
            font-weight: 300;
        }

        .table {
            background: #fff;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
        }

        .table th {
            background: #ee4d4d;
            color: #fff;
            border: none;
            font-family: "Dosis";
            font-weight: 500;
            text-transform: uppercase;
            font-size: 12px;
        }

        .badge {
            font-family: "Dosis";
            font-weight: 500;
            text-transform: uppercase;
            font-size: 10px;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar position-fixed d-none d-md-block">
        <div class="position-sticky pt-3">
            <div class="px-3 mb-3">
                <a href="/admin/dashboard" class="admin-logo text-decoration-none">
                    <img src="{{ url_for('static', path='img/lowhellocyril.jpg') }}" style="height: 30px; width: 30px; border-radius: 8px; margin-right: 8px;">
                    HELLO<span>CYRIL</span>
                </a>
                <div class="mt-2">
                    <small style="color: #f7aaaa;">Admin Panel</small>
                </div>
            </div>

            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/admin/dashboard' %}active{% endif %}" href="/admin/dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/admin/admins' %}active{% endif %}" href="/admin/admins">
                        <i class="fas fa-users-cog me-2"></i>
                        Admin Users
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/admin/groups' %}active{% endif %}" href="/admin/groups">
                        <i class="fas fa-users me-2"></i>
                        Community Groups
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/admin/feedback' %}active{% endif %}" href="/admin/feedback">
                        <i class="fas fa-comments me-2"></i>
                        Feedback
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/admin/analytics' %}active{% endif %}" href="/admin/analytics">
                        <i class="fas fa-chart-line me-2"></i>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/admin/whatsapp-test">
                        <i class="fab fa-whatsapp me-2"></i>
                        WhatsApp Test
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/admin/whatsapp">
                        <i class="fab fa-whatsapp me-2"></i>
                        WhatsApp Tools
                    </a>
                </li>
                <li class="nav-item mt-3">
                    <hr style="color: #f7aaaa;">
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>
                        View Site
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="logout()" style="color: #ee4d4d;">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main content -->
    <div class="main-content">
        <!-- Top navbar -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
            <div class="container-fluid">
                <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page content -->
        <div class="container-fluid py-4">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('admin_token');
                window.location.href = '/admin/login';
            }
        }

        // Check if user is authenticated
        function checkAuth() {
            const token = localStorage.getItem('admin_token');
            if (!token && !window.location.pathname.includes('/admin/login')) {
                window.location.href = '/admin/login';
            }
        }

        // Add token to API requests
        function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('admin_token');
            if (token) {
                options.headers = {
                    ...options.headers,
                    'Authorization': `Bearer ${token}`
                };
            }
            return fetch(url, options);
        }

        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (!window.location.pathname.includes('/admin/login')) {
                checkAuth();
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
