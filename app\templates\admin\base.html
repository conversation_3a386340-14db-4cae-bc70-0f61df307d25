<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Panel - Hello Cyril{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
        }
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: #495057;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #007bff;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .btn-outline-danger:hover {
            color: #fff;
            background-color: #dc3545;
            border-color: #dc3545;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar position-fixed d-none d-md-block">
        <div class="position-sticky pt-3">
            <div class="px-3 mb-3">
                <h5 class="text-white">
                    <i class="fas fa-shield-alt me-2"></i>
                    Hello Cyril Admin
                </h5>
            </div>
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/admin/dashboard' %}active{% endif %}" href="/admin/dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/admin/admins' %}active{% endif %}" href="/admin/admins">
                        <i class="fas fa-users-cog me-2"></i>
                        Admin Users
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/admin/feedback' %}active{% endif %}" href="/admin/feedback">
                        <i class="fas fa-comments me-2"></i>
                        Feedback
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/admin/whatsapp-test">
                        <i class="fab fa-whatsapp me-2"></i>
                        WhatsApp Test
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/admin/whatsapp">
                        <i class="fab fa-whatsapp me-2"></i>
                        WhatsApp Tools
                    </a>
                </li>
                <li class="nav-item mt-3">
                    <hr class="text-white">
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>
                        View Site
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-danger" href="#" onclick="logout()">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main content -->
    <div class="main-content">
        <!-- Top navbar -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
            <div class="container-fluid">
                <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page content -->
        <div class="container-fluid py-4">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('admin_token');
                window.location.href = '/admin/login';
            }
        }

        // Check if user is authenticated
        function checkAuth() {
            const token = localStorage.getItem('admin_token');
            if (!token && !window.location.pathname.includes('/admin/login')) {
                window.location.href = '/admin/login';
            }
        }

        // Add token to API requests
        function makeAuthenticatedRequest(url, options = {}) {
            const token = localStorage.getItem('admin_token');
            if (token) {
                options.headers = {
                    ...options.headers,
                    'Authorization': `Bearer ${token}`
                };
            }
            return fetch(url, options);
        }

        // Check authentication on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (!window.location.pathname.includes('/admin/login')) {
                checkAuth();
            }
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
