{% extends "admin/base.html" %}

{% block title %}Dashboard - Admin Panel{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            Admin Dashboard
        </h1>
    </div>
</div>

<div class="row">
    <!-- Stats Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Reports
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-reports">
                            Loading...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Admins
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-admins">
                            Loading...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users-cog fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Feedback Items
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="feedback-count">
                            Loading...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-comments fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Community Groups
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="groups-count">
                            Loading...
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="/admin/admins" class="btn btn-primary btn-block w-100">
                            <i class="fas fa-users-cog me-2"></i>
                            Manage Admins
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/admin/groups" class="btn btn-primary btn-block w-100">
                            <i class="fas fa-users me-2"></i>
                            Community Groups
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/admin/feedback" class="btn btn-primary btn-block w-100">
                            <i class="fas fa-comments me-2"></i>
                            View Feedback
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/admin/analytics" class="btn btn-primary btn-block w-100">
                            <i class="fas fa-chart-line me-2"></i>
                            Analytics
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/admin/whatsapp-test" class="btn btn-success btn-block w-100">
                            <i class="fab fa-whatsapp me-2"></i>
                            WhatsApp Test
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="https://app.yoco.com/sales/payments/yoco-online" target="_blank" class="btn btn-success btn-block w-100">
                            <i class="fab fa-whatsapp me-2"></i>
                            Yoco
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="/" target="_blank" class="btn btn-outline-primary btn-block w-100">
                            <i class="fas fa-external-link-alt me-2"></i>
                            View Site
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Feedback -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-comments me-2"></i>
                    Recent Feedback
                </h6>
            </div>
            <div class="card-body">
                <div id="recent-feedback">
                    Loading...
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .text-xs {
        font-size: 0.7rem;
    }
    .text-gray-800 {
        color: #5a5c69 !important;
    }
    .text-gray-300 {
        color: #dddfeb !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        loadDashboardData();
    });

    async function loadDashboardData() {
        try {
            // Load stats
            const [reportsResponse, adminsResponse, feedbackResponse, groupsResponse] = await Promise.all([
                makeAuthenticatedRequest('/api/reports/'),
                makeAuthenticatedRequest('/api/admin/admins'),
                makeAuthenticatedRequest('/api/admin/feedback'),
                makeAuthenticatedRequest('/api/groups/')
            ]);

            if (reportsResponse.ok) {
                const reports = await reportsResponse.json();
                document.getElementById('total-reports').textContent = reports.length;
            }

            if (adminsResponse.ok) {
                const admins = await adminsResponse.json();
                const activeAdmins = admins.filter(admin => admin.is_active);
                document.getElementById('active-admins').textContent = activeAdmins.length;
            }

            if (feedbackResponse.ok) {
                const feedback = await feedbackResponse.json();
                document.getElementById('feedback-count').textContent = feedback.length;

                // Show recent feedback
                const recentFeedback = feedback.slice(0, 5);
                const feedbackHtml = recentFeedback.map(item => `
                    <div class="mb-2 p-2 border-bottom">
                        <strong>${item.title}</strong><br>
                        <small class="text-muted">${item.email} - ${new Date(item.submitted_at).toLocaleDateString()}</small>
                    </div>
                `).join('');

                document.getElementById('recent-feedback').innerHTML = feedbackHtml || '<p class="text-muted">No feedback yet.</p>';
            }

            if (groupsResponse.ok) {
                const groups = await groupsResponse.json();
                document.getElementById('groups-count').textContent = groups.length;
            }

        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }
</script>
{% endblock %}
