<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Debug - Hello Cyril</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1>Admin Authentication Debug</h1>
                <p>This page helps debug authentication issues.</p>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Authentication Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="auth-status">Checking...</div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Test API Endpoints</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary me-2" onclick="testAuthDebug()">Test /api/auth/debug</button>
                        <button class="btn btn-info me-2" onclick="testTokenDebug()">Test Token Validation</button>
                        <button class="btn btn-warning me-2" onclick="testHeaders()">Test Headers</button>
                        <button class="btn btn-primary me-2" onclick="testAuthMe()">Test /api/auth/me</button>
                        <button class="btn btn-success me-2" onclick="testAuthMeAlt()">Test /api/auth/me-alt</button>
                        <button class="btn btn-primary me-2" onclick="testAdmins()">Test /api/admin/admins</button>
                        <button class="btn btn-danger" onclick="clearToken()">Clear Token</button>

                        <div id="test-results" class="mt-3"></div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Manual Login Test</h5>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" value="admin">
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" value="admin123">
                            </div>
                            <button type="submit" class="btn btn-success">Test Login</button>
                        </form>

                        <div id="login-results" class="mt-3"></div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5>Environment Info</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Current Domain:</strong> <span id="current-domain"></span></p>
                        <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                        <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Display environment info
        document.getElementById('current-domain').textContent = window.location.hostname;
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Check authentication status
        function checkAuthStatus() {
            const token = localStorage.getItem('admin_token');
            const statusDiv = document.getElementById('auth-status');

            if (token) {
                statusDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        Token found in localStorage
                        <br><small>Token: ${token.substring(0, 50)}...</small>
                    </div>
                `;
            } else {
                statusDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No token found in localStorage
                    </div>
                `;
            }
        }

        // Test auth debug endpoint
        async function testAuthDebug() {
            try {
                const response = await fetch('/api/auth/debug');
                const data = await response.json();

                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-info">
                        <strong>/api/auth/debug Response:</strong>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Test token validation
        async function testTokenDebug() {
            try {
                const token = localStorage.getItem('admin_token');

                if (!token) {
                    document.getElementById('test-results').innerHTML = `
                        <div class="alert alert-warning">
                            <strong>No Token:</strong> Please login first to get a token
                        </div>
                    `;
                    return;
                }

                const response = await fetch('/api/auth/debug-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token: token })
                });

                const data = await response.json();

                let alertClass = 'alert-info';
                if (data.status === 'token_valid') {
                    alertClass = 'alert-success';
                } else if (data.status === 'token_expired' || data.status === 'token_invalid') {
                    alertClass = 'alert-warning';
                } else {
                    alertClass = 'alert-danger';
                }

                document.getElementById('test-results').innerHTML = `
                    <div class="alert ${alertClass}">
                        <strong>Token Validation Result:</strong>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Test headers
        async function testHeaders() {
            try {
                const token = localStorage.getItem('admin_token');
                const headers = {};

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/api/auth/debug-headers', { headers });
                const data = await response.json();

                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-info">
                        <strong>Headers Debug:</strong>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Test auth me endpoint
        async function testAuthMe() {
            try {
                const token = localStorage.getItem('admin_token');
                const headers = {};

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/api/auth/me', { headers });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('test-results').innerHTML = `
                        <div class="alert alert-success">
                            <strong>/api/auth/me Response:</strong>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.json();
                    document.getElementById('test-results').innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Error ${response.status}:</strong> ${JSON.stringify(errorData, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Test auth me alternative endpoint
        async function testAuthMeAlt() {
            try {
                const token = localStorage.getItem('admin_token');
                const headers = {};

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/api/auth/me-alt', { headers });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('test-results').innerHTML = `
                        <div class="alert alert-success">
                            <strong>/api/auth/me-alt Response:</strong>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.json();
                    document.getElementById('test-results').innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Error ${response.status}:</strong> ${JSON.stringify(errorData, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Test admins endpoint
        async function testAdmins() {
            try {
                const token = localStorage.getItem('admin_token');
                const headers = {};

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/api/admin/admins', { headers });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('test-results').innerHTML = `
                        <div class="alert alert-success">
                            <strong>/api/admin/admins Response:</strong>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.json();
                    document.getElementById('test-results').innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Error ${response.status}:</strong> ${JSON.stringify(errorData, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('test-results').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // Clear token
        function clearToken() {
            localStorage.removeItem('admin_token');
            checkAuthStatus();
            document.getElementById('test-results').innerHTML = `
                <div class="alert alert-info">
                    Token cleared from localStorage
                </div>
            `;
        }

        // Test login
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    localStorage.setItem('admin_token', data.access_token);
                    document.getElementById('login-results').innerHTML = `
                        <div class="alert alert-success">
                            <strong>Login Successful!</strong>
                            <br>Token stored in localStorage
                            <br><small>Token: ${data.access_token.substring(0, 50)}...</small>
                        </div>
                    `;
                    checkAuthStatus();
                } else {
                    document.getElementById('login-results').innerHTML = `
                        <div class="alert alert-danger">
                            <strong>Login Failed:</strong> ${data.detail || 'Unknown error'}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('login-results').innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        });

        // Check auth status on page load
        document.addEventListener('DOMContentLoaded', checkAuthStatus);
    </script>
</body>
</html>
