{% extends "admin/base.html" %}

{% block title %}Feedback - Admin Panel{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-comments me-2"></i>
            Feedback Management
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="feedbackTable">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Email</th>
                                <th>Submitted</th>
                                <th>Preview</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="feedbackTableBody">
                            <tr>
                                <td colspan="5" class="text-center">Loading...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View/Edit Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-comment me-2"></i>
                    Feedback Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="feedbackForm">
                <div class="modal-body">
                    <input type="hidden" id="feedbackId">
                    
                    <div class="mb-3">
                        <label for="feedbackEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="feedbackEmail" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="feedbackTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="feedbackTitle" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="feedbackComment" class="form-label">Comment</label>
                        <textarea class="form-control" id="feedbackComment" rows="6" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="feedbackSubmitted" class="form-label">Submitted At</label>
                        <input type="text" class="form-control" id="feedbackSubmitted" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" onclick="deleteFeedback()">
                        <i class="fas fa-trash me-2"></i>
                        Delete
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let feedbackList = [];
    let currentFeedbackId = null;

    document.addEventListener('DOMContentLoaded', function() {
        loadFeedback();
        
        // Feedback form handler
        document.getElementById('feedbackForm').addEventListener('submit', handleUpdateFeedback);
    });

    async function loadFeedback() {
        try {
            const response = await makeAuthenticatedRequest('/api/admin/feedback');
            if (response.ok) {
                feedbackList = await response.json();
                renderFeedbackTable();
            } else {
                showAlert('Error loading feedback', 'danger');
            }
        } catch (error) {
            showAlert('Network error loading feedback', 'danger');
        }
    }

    function renderFeedbackTable() {
        const tbody = document.getElementById('feedbackTableBody');
        
        if (feedbackList.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">No feedback found</td></tr>';
            return;
        }

        tbody.innerHTML = feedbackList.map(feedback => `
            <tr>
                <td>
                    <strong>${feedback.title}</strong>
                </td>
                <td>${feedback.email}</td>
                <td>${new Date(feedback.submitted_at).toLocaleDateString()}</td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;">
                        ${feedback.comment}
                    </div>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewFeedback('${feedback.id}')">
                        <i class="fas fa-eye"></i>
                        View
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="confirmDeleteFeedback('${feedback.id}', '${feedback.title}')">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                </td>
            </tr>
        `).join('');
    }

    async function viewFeedback(feedbackId) {
        try {
            const response = await makeAuthenticatedRequest(`/api/admin/feedback/${feedbackId}`);
            if (response.ok) {
                const feedback = await response.json();
                
                currentFeedbackId = feedback.id;
                document.getElementById('feedbackId').value = feedback.id;
                document.getElementById('feedbackEmail').value = feedback.email;
                document.getElementById('feedbackTitle').value = feedback.title;
                document.getElementById('feedbackComment').value = feedback.comment;
                document.getElementById('feedbackSubmitted').value = new Date(feedback.submitted_at).toLocaleString();
                
                new bootstrap.Modal(document.getElementById('feedbackModal')).show();
            } else {
                showAlert('Error loading feedback details', 'danger');
            }
        } catch (error) {
            showAlert('Network error loading feedback details', 'danger');
        }
    }

    async function handleUpdateFeedback(e) {
        e.preventDefault();
        
        const feedbackId = document.getElementById('feedbackId').value;
        const title = document.getElementById('feedbackTitle').value;
        const comment = document.getElementById('feedbackComment').value;

        try {
            const response = await makeAuthenticatedRequest(`/api/admin/feedback/${feedbackId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title, comment })
            });

            if (response.ok) {
                showAlert('Feedback updated successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('feedbackModal')).hide();
                loadFeedback();
            } else {
                const error = await response.json();
                showAlert(error.detail || 'Error updating feedback', 'danger');
            }
        } catch (error) {
            showAlert('Network error updating feedback', 'danger');
        }
    }

    function confirmDeleteFeedback(feedbackId, title) {
        if (confirm(`Are you sure you want to delete the feedback "${title}"?`)) {
            deleteFeedbackById(feedbackId);
        }
    }

    async function deleteFeedback() {
        if (currentFeedbackId && confirm('Are you sure you want to delete this feedback?')) {
            await deleteFeedbackById(currentFeedbackId);
            bootstrap.Modal.getInstance(document.getElementById('feedbackModal')).hide();
        }
    }

    async function deleteFeedbackById(feedbackId) {
        try {
            const response = await makeAuthenticatedRequest(`/api/admin/feedback/${feedbackId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                showAlert('Feedback deleted successfully', 'success');
                loadFeedback();
            } else {
                const error = await response.json();
                showAlert(error.detail || 'Error deleting feedback', 'danger');
            }
        } catch (error) {
            showAlert('Network error deleting feedback', 'danger');
        }
    }

    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Insert at the top of the content
        const content = document.querySelector('.container-fluid');
        content.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = content.querySelector('.alert');
            if (alert) {
                bootstrap.Alert.getOrCreateInstance(alert).close();
            }
        }, 5000);
    }
</script>
{% endblock %}
