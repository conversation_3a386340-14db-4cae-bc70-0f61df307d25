{% extends "admin/base.html" %}

{% block title %}Community Groups - Admin Panel{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2"></i>
                Community Groups
            </h1>
            <div>
                <span class="badge bg-info me-2" id="total-groups">0 Groups</span>
                <span class="badge bg-success me-2" id="active-groups">0 Active</span>
                <span class="badge bg-secondary" id="inactive-groups">0 Inactive</span>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header" style="background: #ee4d4d; color: white;">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-list me-2"></i>
                    Groups Management
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="groupsTable">
                        <thead>
                            <tr>
                                <th>Group Name</th>
                                <th>Admin Contact</th>
                                <th>Admin Phone</th>
                                <th>WhatsApp Group</th>
                                <th>Notification Frequency</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="groupsTableBody">
                            <tr>
                                <td colspan="8" class="text-center">Loading...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Group Details Modal -->
<div class="modal fade" id="groupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-users me-2"></i>
                    Group Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Basic Information</h6>
                        <p><strong>Name:</strong> <span id="modalGroupName"></span></p>
                        <p><strong>Description:</strong> <span id="modalGroupDescription"></span></p>
                        <p><strong>Admin Contact:</strong> <span id="modalAdminContact"></span></p>
                        <p><strong>Admin Phone:</strong> <span id="modalAdminPhone"></span></p>
                        <p><strong>WhatsApp Group ID:</strong> <span id="modalWhatsappGroup"></span></p>
                    </div>
                    <div class="col-md-6">
                        <h6>Settings</h6>
                        <p><strong>Notification Frequency:</strong> <span id="modalNotificationFreq"></span></p>
                        <p><strong>Status:</strong> <span id="modalStatus"></span></p>
                        <p><strong>Created:</strong> <span id="modalCreated"></span></p>
                        <p><strong>Categories:</strong> <span id="modalCategories"></span></p>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Coverage Area</h6>
                        <div id="modalMap" style="height: 300px; border-radius: 5px;"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="toggleStatusBtn" onclick="toggleGroupStatus()">
                    <i class="fas fa-toggle-on me-2"></i>
                    Toggle Status
                </button>
                <button type="button" class="btn btn-danger" onclick="deleteGroup()">
                    <i class="fas fa-trash me-2"></i>
                    Delete Group
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
{% endblock %}

{% block extra_js %}
<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>

<script>
    let groups = [];
    let currentGroupId = null;
    let modalMap = null;

    document.addEventListener('DOMContentLoaded', function() {
        loadGroups();
    });

    async function loadGroups() {
        try {
            const response = await makeAuthenticatedRequest('/api/admin/groups');
            if (response.ok) {
                groups = await response.json();
                renderGroupsTable();
                updateStats();
            } else {
                showAlert('Error loading groups', 'danger');
            }
        } catch (error) {
            showAlert('Network error loading groups', 'danger');
        }
    }

    function updateStats() {
        const totalGroups = groups.length;
        const activeGroups = groups.filter(g => g.active).length;
        const inactiveGroups = totalGroups - activeGroups;

        document.getElementById('total-groups').textContent = `${totalGroups} Groups`;
        document.getElementById('active-groups').textContent = `${activeGroups} Active`;
        document.getElementById('inactive-groups').textContent = `${inactiveGroups} Inactive`;
    }

    function renderGroupsTable() {
        const tbody = document.getElementById('groupsTableBody');
        
        if (groups.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center">No groups found</td></tr>';
            return;
        }

        tbody.innerHTML = groups.map(group => `
            <tr>
                <td>
                    <strong>${group.name}</strong>
                    ${group.description ? `<br><small class="text-muted">${group.description.substring(0, 50)}${group.description.length > 50 ? '...' : ''}</small>` : ''}
                </td>
                <td>${group.admin_contact}</td>
                <td>${group.admin_phone || 'N/A'}</td>
                <td>${group.whatsapp_group_id ? '<i class="fab fa-whatsapp text-success"></i> Connected' : '<i class="fas fa-times text-muted"></i> Not connected'}</td>
                <td>
                    <span class="badge ${getFrequencyBadgeClass(group.notification_frequency)}">
                        ${formatFrequency(group.notification_frequency)}
                    </span>
                </td>
                <td>
                    <span class="badge ${group.active ? 'bg-success' : 'bg-secondary'}">
                        ${group.active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${new Date(group.created_at).toLocaleDateString()}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewGroup('${group.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm ${group.active ? 'btn-outline-warning' : 'btn-outline-success'}" onclick="quickToggleStatus('${group.id}')">
                        <i class="fas ${group.active ? 'fa-pause' : 'fa-play'}"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="confirmDeleteGroup('${group.id}', '${group.name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    function getFrequencyBadgeClass(frequency) {
        switch(frequency) {
            case 'REAL_TIME': return 'bg-danger';
            case 'HOURLY': return 'bg-warning';
            case 'DAILY': return 'bg-info';
            default: return 'bg-secondary';
        }
    }

    function formatFrequency(frequency) {
        switch(frequency) {
            case 'REAL_TIME': return 'Real Time';
            case 'HOURLY': return 'Hourly';
            case 'DAILY': return 'Daily';
            default: return frequency;
        }
    }

    function viewGroup(groupId) {
        const group = groups.find(g => g.id === groupId);
        if (!group) return;

        currentGroupId = groupId;

        // Populate modal
        document.getElementById('modalGroupName').textContent = group.name;
        document.getElementById('modalGroupDescription').textContent = group.description || 'No description';
        document.getElementById('modalAdminContact').textContent = group.admin_contact;
        document.getElementById('modalAdminPhone').textContent = group.admin_phone || 'Not provided';
        document.getElementById('modalWhatsappGroup').textContent = group.whatsapp_group_id || 'Not connected';
        document.getElementById('modalNotificationFreq').textContent = formatFrequency(group.notification_frequency);
        document.getElementById('modalStatus').innerHTML = `<span class="badge ${group.active ? 'bg-success' : 'bg-secondary'}">${group.active ? 'Active' : 'Inactive'}</span>`;
        document.getElementById('modalCreated').textContent = new Date(group.created_at).toLocaleString();
        
        // Parse and display categories
        let categories = 'None';
        if (group.categories_json) {
            try {
                const cats = JSON.parse(group.categories_json);
                categories = cats.join(', ');
            } catch (e) {
                categories = 'Error parsing categories';
            }
        }
        document.getElementById('modalCategories').textContent = categories;

        // Update toggle button
        const toggleBtn = document.getElementById('toggleStatusBtn');
        toggleBtn.innerHTML = `<i class="fas fa-toggle-${group.active ? 'off' : 'on'} me-2"></i>${group.active ? 'Deactivate' : 'Activate'}`;
        toggleBtn.className = `btn ${group.active ? 'btn-warning' : 'btn-success'}`;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('groupModal'));
        modal.show();

        // Initialize map after modal is shown
        setTimeout(() => {
            initModalMap(group);
        }, 300);
    }

    function initModalMap(group) {
        if (modalMap) {
            modalMap.remove();
        }

        modalMap = L.map('modalMap').setView([-25.7461, 28.1881], 10);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(modalMap);

        // Parse and display area
        if (group.area_json) {
            try {
                const coordinates = JSON.parse(group.area_json);
                if (coordinates && coordinates.length > 0) {
                    const polygon = L.polygon(coordinates.map(coord => [coord[1], coord[0]]), {
                        color: '#ee4d4d',
                        fillColor: '#ee4d4d',
                        fillOpacity: 0.3
                    }).addTo(modalMap);
                    
                    modalMap.fitBounds(polygon.getBounds());
                }
            } catch (e) {
                console.error('Error parsing area coordinates:', e);
            }
        }
    }

    async function quickToggleStatus(groupId) {
        await toggleGroupStatusById(groupId);
    }

    async function toggleGroupStatus() {
        if (currentGroupId) {
            await toggleGroupStatusById(currentGroupId);
            bootstrap.Modal.getInstance(document.getElementById('groupModal')).hide();
        }
    }

    async function toggleGroupStatusById(groupId) {
        try {
            const response = await makeAuthenticatedRequest(`/api/admin/groups/${groupId}/toggle-active`, {
                method: 'PUT'
            });

            if (response.ok) {
                const result = await response.json();
                showAlert(result.message, 'success');
                loadGroups();
            } else {
                const error = await response.json();
                showAlert(error.detail || 'Error toggling group status', 'danger');
            }
        } catch (error) {
            showAlert('Network error toggling group status', 'danger');
        }
    }

    function confirmDeleteGroup(groupId, groupName) {
        if (confirm(`Are you sure you want to delete the group "${groupName}"? This action cannot be undone.`)) {
            deleteGroupById(groupId);
        }
    }

    async function deleteGroup() {
        if (currentGroupId && confirm('Are you sure you want to delete this group? This action cannot be undone.')) {
            await deleteGroupById(currentGroupId);
            bootstrap.Modal.getInstance(document.getElementById('groupModal')).hide();
        }
    }

    async function deleteGroupById(groupId) {
        try {
            const response = await makeAuthenticatedRequest(`/api/admin/groups/${groupId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                showAlert('Group deleted successfully', 'success');
                loadGroups();
            } else {
                const error = await response.json();
                showAlert(error.detail || 'Error deleting group', 'danger');
            }
        } catch (error) {
            showAlert('Network error deleting group', 'danger');
        }
    }

    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const content = document.querySelector('.container-fluid');
        content.insertAdjacentHTML('afterbegin', alertHtml);
        
        setTimeout(() => {
            const alert = content.querySelector('.alert');
            if (alert) {
                bootstrap.Alert.getOrCreateInstance(alert).close();
            }
        }, 5000);
    }
</script>
{% endblock %}
