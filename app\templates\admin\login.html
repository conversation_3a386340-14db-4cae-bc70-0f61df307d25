<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Hello Cyril</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        /* Import main website styles */
        @import "https://fonts.googleapis.com/css?family=Dosis:300,400,500,600,700";

        body {
            background: #f9f9f9;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: "Dosis", helvetica, arial, tahoma, verdana;
            color: #726f77;
        }

        .login-card {
            background: white;
            border-radius: 5px;
            box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }

        .login-header {
            background: #2b2e48;
            color: #ee4d4d;
            padding: 2rem;
            text-align: center;
        }

        .login-body {
            padding: 2rem;
        }

        .form-control {
            border-radius: 5px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-family: "Dosis";
        }

        .form-control:focus {
            border-color: #ee4d4d;
            box-shadow: 0 0 0 0.2rem rgba(238, 77, 77, 0.25);
        }

        .btn-login {
            background: #ee4d4d;
            border: 2px solid #ee4d4d;
            color: #fff;
            border-radius: 5px;
            padding: 0.75rem 2rem;
            font-family: "Dosis";
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 12px;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 0 #f27c7c;
        }

        .btn-login:hover {
            background: #f27c7c;
            border-color: #f27c7c;
            box-shadow: none;
            transform: translate(2px, 2px);
        }

        .alert {
            border-radius: 5px;
            font-family: "Dosis";
        }

        .admin-logo {
            color: #ee4d4d;
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 22px;
            font-weight: 500;
            text-decoration: none;
        }

        .admin-logo > span {
            color: #f7aaaa;
            font-weight: 300;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: "Dosis", arial, tahoma, verdana;
            font-weight: 500;
        }

        label {
            font-family: "Dosis";
            font-weight: 500;
            color: #726f77;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <div class="admin-logo mb-2">
                <img src="{{ url_for('static', path='img/lowhellocyril.jpg') }}" style="height: 40px; width: 40px; border-radius: 10px; margin-right: 10px;">
                HELLO<span>CYRIL</span>
            </div>
            <p class="mb-0 mt-2" style="color: #f7aaaa; font-family: 'Dosis';">Admin Panel</p>
        </div>

        <div class="login-body">
            <div id="alert-container"></div>

            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>Username
                    </label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>Password
                    </label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Login
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alertContainer = document.getElementById('alert-container');

            // Clear previous alerts
            alertContainer.innerHTML = '';

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok) {
                    // Store token in both localStorage and cookie
                    localStorage.setItem('admin_token', data.access_token);

                    // Set cookie as backup (for reverse proxy issues)
                    document.cookie = `admin_token=${data.access_token}; path=/; max-age=28800; secure; samesite=strict`;

                    // Show success message
                    alertContainer.innerHTML = `
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            Login successful! Redirecting...
                        </div>
                    `;

                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = '/admin/dashboard';
                    }, 1000);
                } else {
                    // Show error message
                    alertContainer.innerHTML = `
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${data.detail || 'Login failed'}
                        </div>
                    `;
                }
            } catch (error) {
                alertContainer.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Network error. Please try again.
                    </div>
                `;
            }
        });

        // Check if already logged in
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('admin_token');
            if (token) {
                // Verify token is still valid
                fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.href = '/admin/dashboard';
                    } else {
                        localStorage.removeItem('admin_token');
                    }
                })
                .catch(() => {
                    localStorage.removeItem('admin_token');
                });
            }
        });
    </script>
</body>
</html>
