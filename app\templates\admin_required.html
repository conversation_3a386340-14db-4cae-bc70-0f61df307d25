<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Access Required - Hello Cyril</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .access-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        .access-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .access-body {
            padding: 2rem;
            text-align: center;
        }
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        .btn-home {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
        .icon-large {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="access-card">
        <div class="access-header">
            <i class="fas fa-lock icon-large"></i>
            <h3 class="mb-0">Admin Access Required</h3>
            <p class="mb-0 mt-2 opacity-75">Restricted Area</p>
        </div>
        
        <div class="access-body">
            <h4 class="mb-3">WhatsApp Tools Access</h4>
            <p class="text-muted mb-4">
                The WhatsApp testing and management tools have been moved to the admin panel for security reasons. 
                You need administrator privileges to access these features.
            </p>
            
            <div class="mb-4">
                <i class="fas fa-info-circle text-info me-2"></i>
                <strong>What you can do:</strong>
            </div>
            
            <ul class="list-unstyled text-start mb-4">
                <li class="mb-2">
                    <i class="fas fa-check text-success me-2"></i>
                    Login as an administrator to access WhatsApp tools
                </li>
                <li class="mb-2">
                    <i class="fas fa-check text-success me-2"></i>
                    Manage admin users and system settings
                </li>
                <li class="mb-2">
                    <i class="fas fa-check text-success me-2"></i>
                    View and manage user feedback
                </li>
                <li class="mb-2">
                    <i class="fas fa-check text-success me-2"></i>
                    Access advanced WhatsApp testing features
                </li>
            </ul>
            
            <div class="d-flex flex-column flex-sm-row justify-content-center">
                <a href="/admin/login" class="btn-admin">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Admin Login
                </a>
                <a href="/" class="btn-home">
                    <i class="fas fa-home me-2"></i>
                    Back to Home
                </a>
            </div>
            
            <div class="mt-4 pt-3 border-top">
                <small class="text-muted">
                    <i class="fas fa-question-circle me-1"></i>
                    Need admin access? Contact your system administrator.
                </small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
