{% extends "base.html" %}

{% block title %}API Documentation - Hello <PERSON>{% endblock %}

{% block extra_css %}
    <style>
        .docs-container {
            background: linear-gradient(135deg, #ee4d4d 0%, #f27c7c 100%);
            color: white;
            padding: 60px 0 40px 0;
        }

        .docs-content {
            background: white;
            color: #333;
            border-radius: 5px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px 0;
            font-family: "Dosis", arial, tahoma, verdana;
        }

        .docs-header {
            background: #ee4d4d;
            color: white;
            padding: 30px;
            border-radius: 5px 5px 0 0;
        }

        .docs-body {
            padding: 30px;
        }

        .code-block {
            background: #2b2e48;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            border: 2px solid #f27c7c;
            margin: 15px 0;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ee4d4d;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #d43f3f;
        }

        .endpoint-card {
            border: 2px solid #f27c7c;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
        }

        .endpoint-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 2px solid #f27c7c;
        }

        .endpoint-body {
            padding: 20px;
        }

        .method-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }

        .method-get { background: #28a745; color: white; }
        .method-post { background: #007bff; color: white; }
        .method-delete { background: #dc3545; color: white; }
        .method-ws { background: #6f42c1; color: white; }

        .nav-tabs .nav-link {
            color: #ee4d4d;
            border: 2px solid transparent;
        }

        .nav-tabs .nav-link.active {
            background: #ee4d4d;
            color: white;
            border-color: #ee4d4d;
        }

        .alert-info {
            background: #e8f4fd;
            border: 2px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }

        .btn-hello-cyril {
            background: transparent;
            border: 2px solid #f27c7c;
            color: #f27c7c;
            border-radius: 5px;
            text-transform: uppercase;
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 12px;
            box-shadow: 2px 2px 0 #f27c7c;
            transition: all 0.3s ease;
            position: relative;
            padding: 10px 20px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-hello-cyril:hover {
            background: transparent;
            border-color: #f27c7c;
            color: #f27c7c;
            box-shadow: none;
            top: 2px;
            left: 2px;
            text-decoration: none;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="docs-container">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="docs-content">
                        <div class="docs-header text-center">
                            <h1><i class="fas fa-code"></i> Hello Cyril API Documentation</h1>
                            <p class="lead">Complete guide to integrating with the Hello Cyril API</p>
                        </div>

                        <div class="docs-body">
                            <!-- Quick Start -->
                            <section id="quick-start">
                                <h2><i class="fas fa-rocket"></i> Quick Start</h2>
                                <p>Get started with the Hello Cyril API in minutes. All endpoints require an API key obtained through our subscription service.</p>

                                <div class="alert-info">
                                    <strong><i class="fas fa-key"></i> API Key Required:</strong>
                                    All API requests must include your API key in the <code>X-API-Key</code> header.
                                    <a href="/subscription" class="btn-hello-cyril ms-2">Get API Key</a>
                                </div>

                                <h4>Base URL</h4>
                                <div class="code-block">
                                    <button class="copy-btn" onclick="copyCode(this)">Copy</button>
https://hellocyril.co.za/api/subscription/
                                </div>
                            </section>

                            <!-- Authentication -->
                            <section id="authentication">
                                <h2><i class="fas fa-shield-alt"></i> Authentication</h2>
                                <p>Include your API key in the request header:</p>

                                <div class="code-block">
                                    <button class="copy-btn" onclick="copyCode(this)">Copy</button>
X-API-Key: hc_your_api_key_here
                                </div>
                            </section>

                            <!-- Code Examples -->
                            <section id="examples">
                                <h2><i class="fas fa-code"></i> Code Examples</h2>

                                <!-- Tabs for different languages -->
                                <ul class="nav nav-tabs" id="languageTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="javascript-tab" data-bs-toggle="tab" data-bs-target="#javascript" type="button" role="tab">
                                            <i class="fab fa-js-square"></i> JavaScript
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="python-tab" data-bs-toggle="tab" data-bs-target="#python" type="button" role="tab">
                                            <i class="fab fa-python"></i> Python
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="curl-tab" data-bs-toggle="tab" data-bs-target="#curl" type="button" role="tab">
                                            <i class="fas fa-terminal"></i> cURL
                                        </button>
                                    </li>
                                </ul>

                                <div class="tab-content" id="languageTabContent">
                                    <!-- JavaScript Examples -->
                                    <div class="tab-pane fade show active" id="javascript" role="tabpanel">
                                        <h4>JavaScript/Node.js Example</h4>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                                            // Basic API request
                                            const API_KEY = 'hc_your_api_key_here';
                                            const BASE_URL = 'https://hellocyril.co.za/api/subscription';

                                            async function getReports() {
                                                try {
                                                    const response = await fetch(`${BASE_URL}/reports?category=crime&limit=10`, {
                                                        headers: {
                                                            'X-API-Key': API_KEY,
                                                            'Content-Type': 'application/json'
                                                        }
                                                    });

                                                    if (!response.ok) {
                                                        throw new Error(`HTTP error! status: ${response.status}`);
                                                    }

                                                    const data = await response.json();
                                                    console.log('Reports:', data.reports);
                                                    return data;
                                                } catch (error) {
                                                    console.error('Error fetching reports:', error);
                                                }
                                            }

                                            // WebSocket connection for real-time alerts
                                            function connectWebSocket() {
                                                const ws = new WebSocket(`wss://hellocyril.co.za/api/subscription/ws?api_key=${API_KEY}`);

                                                ws.onopen = function(event) {
                                                    console.log('Connected to Hello Cyril WebSocket');

                                                    // Send ping every 30 seconds to keep connection alive
                                                    setInterval(() => {
                                                        ws.send(JSON.stringify({type: 'ping'}));
                                                    }, 30000);
                                                };

                                                ws.onmessage = function(event) {
                                                    const message = JSON.parse(event.data);
                                                    console.log('Received:', message);

                                                    if (message.type === 'alert') {
                                                        handleNewAlert(message.data);
                                                    }
                                                };

                                                ws.onclose = function(event) {
                                                    console.log('WebSocket connection closed');
                                                    // Reconnect after 5 seconds
                                                    setTimeout(connectWebSocket, 5000);
                                                };

                                                return ws;
                                            }

                                            function handleNewAlert(alertData) {
                                                console.log('New alert received:', alertData);
                                                // Handle the alert in your application
                                            }

                                            // Usage
                                            getReports();
                                            const websocket = connectWebSocket();
                                        </div>
                                    </div>

                                    <!-- Python Examples -->
                                    <div class="tab-pane fade" id="python" role="tabpanel">
                                        <h4>Python Example</h4>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
import requests
import websocket
import json
import threading
import time

API_KEY = 'hc_your_api_key_here'
BASE_URL = 'https://hellocyril.co.za/api/subscription'

class HelloCyrilAPI:
    def __init__(self, api_key):
        self.api_key = api_key
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }

    def get_reports(self, category=None, limit=100):
        """Get reports from the API"""
        params = {'limit': limit}
        if category:
            params['category'] = category

        response = requests.get(
            f'{BASE_URL}/reports',
            headers=self.headers,
            params=params
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f'API Error: {response.status_code} - {response.text}')

    def get_stats(self, period='overall'):
        """Get platform statistics"""
        response = requests.get(
            f'{BASE_URL}/stats',
            headers=self.headers,
            params={'period': period}
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f'API Error: {response.status_code} - {response.text}')

    def subscribe_webhook(self, webhook_url, alert_types=['all']):
        """Subscribe to webhook notifications"""
        data = {
            'webhook_url': webhook_url,
            'alert_types': alert_types
        }

        response = requests.post(
            f'{BASE_URL}/webhooks/subscribe',
            headers=self.headers,
            json=data
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f'Webhook Error: {response.status_code} - {response.text}')

# WebSocket client for real-time alerts
class HelloCyrilWebSocket:
    def __init__(self, api_key):
        self.api_key = api_key
        self.ws = None

    def on_message(self, ws, message):
        data = json.loads(message)
        print(f"Received: {data}")

        if data.get('type') == 'alert':
            self.handle_alert(data['data'])

    def on_error(self, ws, error):
        print(f"WebSocket error: {error}")

    def on_close(self, ws, close_status_code, close_msg):
        print("WebSocket connection closed")
        # Reconnect after 5 seconds
        time.sleep(5)
        self.connect()

    def on_open(self, ws):
        print("Connected to Hello Cyril WebSocket")

        # Send ping every 30 seconds
        def ping():
            while True:
                time.sleep(30)
                if ws.sock and ws.sock.connected:
                    ws.send(json.dumps({'type': 'ping'}))

        threading.Thread(target=ping, daemon=True).start()

    def handle_alert(self, alert_data):
        print(f"New alert: {alert_data}")
        # Process the alert in your application

    def connect(self):
        ws_url = f"wss://hellocyril.co.za/api/subscription/ws?api_key={self.api_key}"
        self.ws = websocket.WebSocketApp(
            ws_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        self.ws.run_forever()

# Usage example
if __name__ == "__main__":
    # Initialize API client
    api = HelloCyrilAPI(API_KEY)

    try:
        # Get crime reports
        reports = api.get_reports(category='crime', limit=10)
        print(f"Found {len(reports['reports'])} crime reports")

        # Get platform stats
        stats = api.get_stats()
        print(f"Platform stats: {stats}")

        # Subscribe to webhooks (Premium/Enterprise only)
        # webhook_result = api.subscribe_webhook('https://your-app.com/webhook')

    except Exception as e:
        print(f"Error: {e}")

    # Connect to WebSocket for real-time alerts
    # ws_client = HelloCyrilWebSocket(API_KEY)
    # ws_client.connect()
                                        </div>
                                    </div>

                                    <!-- cURL Examples -->
                                    <div class="tab-pane fade" id="curl" role="tabpanel">
                                        <h4>cURL Examples</h4>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
# Get reports
curl -H "X-API-Key: hc_your_api_key_here" \
     "https://hellocyril.co.za/api/subscription/reports?category=crime&limit=10"

# Get statistics
curl -H "X-API-Key: hc_your_api_key_here" \
     "https://hellocyril.co.za/api/subscription/stats"

# Subscribe to webhooks (Premium/Enterprise only)
curl -X POST \
     -H "X-API-Key: hc_your_api_key_here" \
     -H "Content-Type: application/json" \
     -d '{"webhook_url": "https://your-app.com/webhook", "alert_types": ["crime", "ems"]}' \
     "https://hellocyril.co.za/api/subscription/webhooks/subscribe"

# Get webhook status
curl -H "X-API-Key: hc_your_api_key_here" \
     "https://hellocyril.co.za/api/subscription/webhooks/status"
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- API Endpoints -->
                            <section id="endpoints">
                                <h2><i class="fas fa-list"></i> API Endpoints</h2>

                                <!-- Reports Endpoint -->
                                <div class="endpoint-card">
                                    <div class="endpoint-header">
                                        <span class="method-badge method-get">GET</span>
                                        <strong>/reports</strong>
                                        <span class="text-muted">- Get crime, EMS, and infrastructure reports</span>
                                    </div>
                                    <div class="endpoint-body">
                                        <h5>Parameters</h5>
                                        <ul>
                                            <li><code>category</code> (optional): Filter by category (crime, ems, infrastructure)</li>
                                            <li><code>start_date</code> (optional): Start date in ISO format</li>
                                            <li><code>end_date</code> (optional): End date in ISO format</li>
                                            <li><code>limit</code> (optional): Number of results (max 1000, default 100)</li>
                                            <li><code>offset</code> (optional): Pagination offset (default 0)</li>
                                        </ul>

                                        <h5>Example Response</h5>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "reports": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "category": "crime",
      "description": "Suspicious activity reported",
      "location": {
        "latitude": -25.7461,
        "longitude": 28.1881
      },
      "timestamp": "2024-01-01T12:00:00Z",
      "verified": true
    }
  ],
  "total": 1,
  "offset": 0,
  "limit": 100
}
                                        </div>
                                    </div>
                                </div>

                                <!-- Stats Endpoint -->
                                <div class="endpoint-card">
                                    <div class="endpoint-header">
                                        <span class="method-badge method-get">GET</span>
                                        <strong>/stats</strong>
                                        <span class="text-muted">- Get platform statistics</span>
                                    </div>
                                    <div class="endpoint-body">
                                        <h5>Parameters</h5>
                                        <ul>
                                            <li><code>period</code> (optional): Time period (overall, daily, weekly, monthly)</li>
                                        </ul>

                                        <h5>Example Response</h5>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "stats": {
    "total_reports": 1250,
    "crime_reports": 800,
    "ems_reports": 300,
    "infrastructure_reports": 150,
    "verified_reports": 1100
  },
  "period": "overall",
  "timestamp": "2024-01-01T12:00:00Z"
}
                                        </div>
                                    </div>
                                </div>

                                <!-- WebSocket Endpoint -->
                                <div class="endpoint-card">
                                    <div class="endpoint-header">
                                        <span class="method-badge method-ws">WS</span>
                                        <strong>/ws</strong>
                                        <span class="text-muted">- Real-time alerts via WebSocket</span>
                                    </div>
                                    <div class="endpoint-body">
                                        <h5>Connection</h5>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
wss://hellocyril.co.za/api/subscription/ws?api_key=hc_your_api_key_here
                                        </div>

                                        <h5>Message Types</h5>
                                        <ul>
                                            <li><strong>connection</strong>: Welcome message when connected</li>
                                            <li><strong>alert</strong>: New report alert</li>
                                            <li><strong>ping/pong</strong>: Keep-alive messages</li>
                                        </ul>

                                        <h5>Example Alert Message</h5>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "type": "alert",
  "data": {
    "report_id": "123e4567-e89b-12d3-a456-426614174000",
    "category": "crime",
    "description": "Break-in reported",
    "location": {
      "latitude": -25.7461,
      "longitude": 28.1881
    },
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
                                        </div>
                                    </div>
                                </div>

                                <!-- Webhooks -->
                                <div class="endpoint-card">
                                    <div class="endpoint-header">
                                        <span class="method-badge method-post">POST</span>
                                        <strong>/webhooks/subscribe</strong>
                                        <span class="text-muted">- Subscribe to webhook notifications (Premium/Enterprise)</span>
                                    </div>
                                    <div class="endpoint-body">
                                        <h5>Request Body</h5>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "webhook_url": "https://your-app.com/webhook",
  "alert_types": ["crime", "ems"],
  "geographic_filter": {
    "center": {
      "latitude": -25.7461,
      "longitude": 28.1881
    },
    "radius_km": 10
  }
}
                                        </div>

                                        <h5>Webhook Payload</h5>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "event_type": "new_report",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "report_id": "123e4567-e89b-12d3-a456-426614174000",
    "category": "crime",
    "description": "Suspicious activity",
    "location": {
      "latitude": -25.7461,
      "longitude": 28.1881
    },
    "verified": false
  }
}
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Rate Limits -->
                            <section id="rate-limits">
                                <h2><i class="fas fa-tachometer-alt"></i> Rate Limits & Pricing</h2>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="endpoint-card">
                                            <div class="endpoint-header">
                                                <h5>Basic Plan - R99/month</h5>
                                            </div>
                                            <div class="endpoint-body">
                                                <ul>
                                                    <li>10,000 requests/month</li>
                                                    <li>100 requests/minute</li>
                                                    <li>Reports API access</li>
                                                    <li>Basic statistics</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="endpoint-card">
                                            <div class="endpoint-header">
                                                <h5>Premium Plan - R299/month</h5>
                                            </div>
                                            <div class="endpoint-body">
                                                <ul>
                                                    <li>50,000 requests/month</li>
                                                    <li>500 requests/minute</li>
                                                    <li>Real-time WebSocket alerts</li>
                                                    <li>Advanced statistics</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="endpoint-card">
                                            <div class="endpoint-header">
                                                <h5>Enterprise Plan - R999/month</h5>
                                            </div>
                                            <div class="endpoint-body">
                                                <ul>
                                                    <li>200,000 requests/month</li>
                                                    <li>2,000 requests/minute</li>
                                                    <li>Webhook notifications</li>
                                                    <li>Custom geographic filters</li>
                                                    <li>Priority support & SLA</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Error Codes -->
                            <section id="errors">
                                <h2><i class="fas fa-exclamation-triangle"></i> Error Codes</h2>

                                <div class="endpoint-card">
                                    <div class="endpoint-body">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Code</th>
                                                    <th>Description</th>
                                                    <th>Solution</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><code>401</code></td>
                                                    <td>Unauthorized - Invalid API key</td>
                                                    <td>Check your API key and ensure it's included in the X-API-Key header</td>
                                                </tr>
                                                <tr>
                                                    <td><code>429</code></td>
                                                    <td>Too Many Requests - Rate limit exceeded</td>
                                                    <td>Reduce request frequency or upgrade your plan</td>
                                                </tr>
                                                <tr>
                                                    <td><code>403</code></td>
                                                    <td>Forbidden - Feature not available in your plan</td>
                                                    <td>Upgrade to Premium or Enterprise plan</td>
                                                </tr>
                                                <tr>
                                                    <td><code>400</code></td>
                                                    <td>Bad Request - Invalid parameters</td>
                                                    <td>Check your request parameters and format</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </section>

                            <!-- Support -->
                            <section id="support">
                                <h2><i class="fas fa-life-ring"></i> Support</h2>
                                <p>Need help with the API? We're here to assist you!</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-envelope"></i> Email Support</h5>
                                        <p>Get help via email for all subscription tiers.</p>
                                        <a href="/feedback" class="btn-hello-cyril">Contact Support</a>
                                    </div>

                                    <div class="col-md-6">
                                        <h5><i class="fas fa-key"></i> Lost API Key?</h5>
                                        <p>Retrieve your API key using your subscription details.</p>
                                        <a href="/api-key-retrieval" class="btn-hello-cyril">Retrieve API Key</a>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        function copyCode(button) {
            // Get the code block content
            const codeBlock = button.parentElement;
            const codeText = codeBlock.textContent.replace('Copy', '').trim();

            // Copy to clipboard
            if (navigator.clipboard) {
                navigator.clipboard.writeText(codeText).then(function() {
                    showCopySuccess(button);
                }).catch(function(err) {
                    console.error('Could not copy text: ', err);
                    fallbackCopyTextToClipboard(codeText, button);
                });
            } else {
                fallbackCopyTextToClipboard(codeText, button);
            }
        }

        function showCopySuccess(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.style.background = '#28a745';

            setTimeout(function() {
                button.innerHTML = originalText;
                button.style.background = '#ee4d4d';
            }, 2000);
        }

        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess(button);
                }
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
                alert('Please manually copy the code');
            }

            document.body.removeChild(textArea);
        }

        // Smooth scrolling for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            // Add navigation menu
            const sections = [
                {id: 'quick-start', title: 'Quick Start'},
                {id: 'authentication', title: 'Authentication'},
                {id: 'examples', title: 'Code Examples'},
                {id: 'endpoints', title: 'API Endpoints'},
                {id: 'rate-limits', title: 'Rate Limits'},
                {id: 'errors', title: 'Error Codes'},
                {id: 'support', title: 'Support'}
            ];

            // Create floating navigation
            const nav = document.createElement('div');
            nav.style.cssText = `
                position: fixed;
                top: 50%;
                right: 20px;
                transform: translateY(-50%);
                background: white;
                border: 2px solid #f27c7c;
                border-radius: 5px;
                padding: 15px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                z-index: 1000;
                max-height: 400px;
                overflow-y: auto;
            `;

            nav.innerHTML = '<h6 style="margin: 0 0 10px 0; color: #ee4d4d;">Navigation</h6>';

            sections.forEach(section => {
                const link = document.createElement('a');
                link.href = `#${section.id}`;
                link.textContent = section.title;
                link.style.cssText = `
                    display: block;
                    color: #ee4d4d;
                    text-decoration: none;
                    padding: 5px 0;
                    font-size: 14px;
                    border-bottom: 1px solid #f27c7c;
                `;
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById(section.id).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
                nav.appendChild(link);
            });

            document.body.appendChild(nav);

            // Hide navigation on mobile
            if (window.innerWidth < 768) {
                nav.style.display = 'none';
            }
        });
    </script>
{% endblock %}
