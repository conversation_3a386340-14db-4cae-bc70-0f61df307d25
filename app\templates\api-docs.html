{% extends "base.html" %}

{% block title %}API Documentation - Hello <PERSON>{% endblock %}

{% block extra_css %}
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .docs-container {
            background: linear-gradient(135deg, #ee4d4d 0%, #f27c7c 100%);
            color: white;
            padding: 60px 0 40px 0;
        }

        .docs-content {
            background: white;
            color: #333;
            border-radius: 5px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px 0;
            font-family: "Dosis", arial, tahoma, verdana;
        }

        .docs-header {
            background: #ee4d4d;
            color: white;
            padding: 30px;
            border-radius: 5px 5px 0 0;
        }

        .docs-body {
            padding: 30px;
        }

        .code-block {
            background: #2b2e48;
            color: #e2e8f0;
            padding: 0;
            border-radius: 5px;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            border: 2px solid #f27c7c;
            margin: 15px 0;
            position: relative;
        }

        .code-block pre {
            margin: 0;
            padding: 20px;
            background: transparent;
            border: none;
            border-radius: 5px;
        }

        .code-block code {
            background: transparent;
            color: inherit;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Enhanced syntax highlighting */
        .code-block .token.comment {
            color: #6a9955;
            font-style: italic;
        }

        .code-block .token.string {
            color: #ce9178;
        }

        .code-block .token.number {
            color: #b5cea8;
        }

        .code-block .token.keyword {
            color: #569cd6;
            font-weight: bold;
        }

        .code-block .token.function {
            color: #dcdcaa;
        }

        .code-block .token.class-name {
            color: #4ec9b0;
        }

        .code-block .token.property {
            color: #9cdcfe;
        }

        .code-block .token.operator {
            color: #d4d4d4;
        }

        .code-block .token.punctuation {
            color: #d4d4d4;
        }

        /* Language-specific styling */
        .language-javascript .token.keyword {
            color: #f92672;
        }

        .language-python .token.keyword {
            color: #66d9ef;
        }

        .language-bash .token.function {
            color: #a6e22e;
        }

        .language-json .token.property {
            color: #f92672;
        }

        .language-json .token.string {
            color: #a6e22e;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ee4d4d;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #d43f3f;
        }

        .endpoint-card {
            border: 2px solid #f27c7c;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
        }

        .endpoint-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 2px solid #f27c7c;
        }

        .endpoint-body {
            padding: 20px;
        }

        .method-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }

        .method-get { background: #28a745; color: white; }
        .method-post { background: #007bff; color: white; }
        .method-delete { background: #dc3545; color: white; }
        .method-ws { background: #6f42c1; color: white; }

        .nav-tabs {
            border-bottom: 3px solid #f27c7c;
            margin-bottom: 0;
        }

        .nav-tabs .nav-link {
            color: #ee4d4d;
            border: 2px solid transparent;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            padding: 12px 20px;
            margin-right: 5px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .nav-tabs .nav-link:hover {
            background: #f27c7c;
            color: white;
            border-color: #f27c7c;
            transform: translateY(-2px);
        }

        .nav-tabs .nav-link.active {
            background: #ee4d4d;
            color: white;
            border-color: #ee4d4d;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(238, 77, 77, 0.3);
        }

        .nav-tabs .nav-link i {
            margin-right: 8px;
            font-size: 16px;
        }

        .tab-content {
            background: white;
            border: 2px solid #f27c7c;
            border-top: none;
            border-radius: 0 0 8px 8px;
            padding: 30px;
            min-height: 400px;
        }

        .tab-pane {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Language-specific tab colors */
        .nav-tabs .nav-link[data-bs-target="#javascript"].active {
            background: #f7df1e;
            color: #000;
            border-color: #f7df1e;
        }

        .nav-tabs .nav-link[data-bs-target="#python"].active {
            background: #3776ab;
            color: white;
            border-color: #3776ab;
        }

        .nav-tabs .nav-link[data-bs-target="#curl"].active {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        /* Language header styling */
        .language-header {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f27c7c;
        }

        .language-header h3 {
            margin-bottom: 15px;
            font-weight: 600;
        }

        .language-header .lead {
            color: #666;
            margin-bottom: 15px;
        }

        .language-header .alert-info {
            background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%);
            border: 2px solid #bee5eb;
            border-radius: 8px;
            margin-bottom: 0;
        }

        .alert-info {
            background: #e8f4fd;
            border: 2px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }

        .btn-hello-cyril {
            background: transparent;
            border: 2px solid #f27c7c;
            color: #f27c7c;
            border-radius: 5px;
            text-transform: uppercase;
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 12px;
            box-shadow: 2px 2px 0 #f27c7c;
            transition: all 0.3s ease;
            position: relative;
            padding: 10px 20px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-hello-cyril:hover {
            background: transparent;
            border-color: #f27c7c;
            color: #f27c7c;
            box-shadow: none;
            top: 2px;
            left: 2px;
            text-decoration: none;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="docs-container">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="docs-content">
                        <div class="docs-header text-center">
                            <h1><i class="fas fa-code"></i> Hello Cyril API Documentation</h1>
                            <p class="lead">Complete guide to integrating with the Hello Cyril API</p>
                        </div>

                        <div class="docs-body">
                            <!-- Quick Start -->
                            <section id="quick-start">
                                <h2><i class="fas fa-rocket"></i> Quick Start</h2>
                                <p>Get started with the Hello Cyril API in minutes. All endpoints require an API key obtained through our subscription service.</p>

                                <div class="alert-info">
                                    <strong><i class="fas fa-key"></i> API Key Required:</strong>
                                    All API requests must include your API key in the <code>X-API-Key</code> header.
                                    <a href="/subscription" class="btn-hello-cyril ms-2">Get API Key</a>
                                </div>

                                <h4>Base URL</h4>
                                <div class="code-block pb-4">
                                    <button class="copy-btn" onclick="copyCode(this)">Copy</button>
https://hellocyril.co.za/api/subscription/
                                </div>
                            </section>

                            <!-- Authentication -->
                            <section id="authentication">
                                <h2><i class="fas fa-shield-alt"></i> Authentication</h2>
                                <p>Include your API key in the request header:</p>

                                <div class="code-block pb-4">
                                    <button class="copy-btn" onclick="copyCode(this)">Copy</button>
X-API-Key: hc_your_api_key_here
                                </div>
                            </section>

                            <!-- Code Examples -->
                            <section id="examples">
                                <h2><i class="fas fa-code"></i> Code Examples</h2>

                                <!-- Enhanced Tabs for different languages -->
                                <div class="mb-4">
                                    <h4><i class="fas fa-code"></i> Choose Your Language</h4>
                                    <p class="text-muted">Select your preferred programming language to see complete integration examples:</p>
                                    <div class="alert alert-info">
                                        <small><strong><i class="fas fa-keyboard"></i> Keyboard Shortcuts:</strong>
                                        Ctrl+1 (JavaScript), Ctrl+2 (Python), Ctrl+3 (cURL) | Ctrl+← → (Navigate tabs)</small>
                                    </div>
                                </div>

                                <ul class="nav nav-tabs" id="languageTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="javascript-tab" data-bs-toggle="tab" data-bs-target="#javascript" type="button" role="tab" aria-controls="javascript" aria-selected="true">
                                            <i class="fab fa-js-square"></i> JavaScript/Node.js
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="python-tab" data-bs-toggle="tab" data-bs-target="#python" type="button" role="tab" aria-controls="python" aria-selected="false">
                                            <i class="fab fa-python"></i> Python
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="curl-tab" data-bs-toggle="tab" data-bs-target="#curl" type="button" role="tab" aria-controls="curl" aria-selected="false">
                                            <i class="fas fa-terminal"></i> cURL/Bash
                                        </button>
                                    </li>
                                </ul>

                                <div class="tab-content" id="languageTabContent">
                                    <!-- JavaScript Examples -->
                                    <div class="tab-pane fade show active" id="javascript" role="tabpanel" aria-labelledby="javascript-tab">
                                        <div class="language-header">
                                            <h3><i class="fab fa-js-square text-warning"></i> JavaScript/Node.js Integration</h3>
                                            <p class="lead">Complete API client with modern async/await, error handling, and WebSocket support for real-time alerts.</p>
                                            <div class="alert alert-info">
                                                <strong><i class="fas fa-lightbulb"></i> Features:</strong>
                                                ES6+ syntax, Promise-based, WebSocket reconnection, comprehensive error handling
                                            </div>
                                        </div>

                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                                            <pre><code class="language-javascript" data-include-snippet="/static/code-snippets/javascript-example.js">
// Hello Cyril API - JavaScript/Node.js Example
const API_KEY = 'hc_your_api_key_here';
const BASE_URL = 'https://hellocyril.co.za/api/subscription';

class HelloCyrilAPI {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseURL = BASE_URL;
        this.headers = {
            'X-API-Key': apiKey,
            'Content-Type': 'application/json'
        };
    }

    async getReports(options = {}) {
        const params = new URLSearchParams(options);

        try {
            const response = await fetch(`${this.baseURL}/reports?${params}`, {
                headers: this.headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Error fetching reports:', error);
            throw error;
        }
    }

    connectWebSocket(onAlert, onError) {
        const wsUrl = `wss://hellocyril.co.za/api/subscription/ws?api_key=${this.apiKey}`;
        const ws = new WebSocket(wsUrl);

        ws.onopen = (event) => {
            console.log('✅ Connected to Hello Cyril WebSocket');

            // Send ping every 30 seconds
            setInterval(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({ type: 'ping' }));
                }
            }, 30000);
        };

        ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            if (message.type === 'alert' && onAlert) {
                onAlert(message.data);
            }
        };

        return ws;
    }
}

// Usage
const api = new HelloCyrilAPI(API_KEY);
const reports = await api.getReports({category: 'crime', limit: 10});
const ws = api.connectWebSocket((alert) => console.log('New alert:', alert));
                                            </code></pre>
                                        </div>

                                        <div class="alert-info">
                                            <strong><i class="fas fa-download"></i> Full Example:</strong>
                                            <a href="/static/code-snippets/javascript-example.js" target="_blank" class="btn-hello-cyril ms-2">Download Complete JS Example</a>
                                        </div>
                                    </div>

                                    <!-- Python Examples -->
                                    <div class="tab-pane fade" id="python" role="tabpanel" aria-labelledby="python-tab">
                                        <div class="language-header">
                                            <h3><i class="fab fa-python text-primary"></i> Python Integration</h3>
                                            <p class="lead">Professional Python client with type hints, session management, and comprehensive error handling.</p>
                                            <div class="alert alert-info">
                                                <strong><i class="fas fa-lightbulb"></i> Features:</strong>
                                                Type hints, requests.Session, WebSocket support, logging, exception handling
                                            </div>
                                        </div>

                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                                            <pre><code class="language-python" data-include-snippet="/static/code-snippets/python-example.py">
import requests
import websocket
import json
import logging
from typing import Dict, List, Optional, Callable

API_KEY = 'hc_your_api_key_here'
BASE_URL = 'https://hellocyril.co.za/api/subscription'

class HelloCyrilAPI:
    """Hello Cyril API Client with comprehensive error handling"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = BASE_URL
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def get_reports(self, category: Optional[str] = None,
                   start_date: Optional[str] = None,
                   end_date: Optional[str] = None,
                   limit: int = 100,
                   offset: int = 0) -> Dict:
        """Get reports with comprehensive filtering options"""
        params = {'limit': limit, 'offset': offset}

        if category:
            params['category'] = category
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date

        try:
            response = self.session.get(f'{self.base_url}/reports', params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logging.error(f"Error fetching reports: {e}")
            raise

class HelloCyrilWebSocket:
    """WebSocket client for real-time alerts"""

    def __init__(self, api_key: str, on_alert: Optional[Callable] = None):
        self.api_key = api_key
        self.on_alert = on_alert
        self.ws = None
        self.running = False

    def connect(self):
        """Connect to WebSocket for real-time alerts"""
        ws_url = f"wss://hellocyril.co.za/api/subscription/ws?api_key={self.api_key}"
        self.running = True

        self.ws = websocket.WebSocketApp(
            ws_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )

        self.ws.run_forever()

# Usage
api = HelloCyrilAPI(API_KEY)
reports = api.get_reports(category='crime', limit=10)
print(f"Found {len(reports['reports'])} reports")

# WebSocket connection
ws_client = HelloCyrilWebSocket(API_KEY)
# ws_client.connect()  # Uncomment to connect
                                            </code></pre>
                                        </div>

                                        <div class="alert-info">
                                            <strong><i class="fas fa-download"></i> Full Example:</strong>
                                            <a href="/static/code-snippets/python-example.py" target="_blank" class="btn-hello-cyril ms-2">Download Complete Python Example</a>
                                        </div>
                                    </div>

                                    <!-- cURL Examples -->
                                    <div class="tab-pane fade" id="curl" role="tabpanel" aria-labelledby="curl-tab">
                                        <div class="language-header">
                                            <h3><i class="fas fa-terminal text-success"></i> cURL/Bash Integration</h3>
                                            <p class="lead">Complete bash script with cURL commands, error handling, and response parsing using jq.</p>
                                            <div class="alert alert-info">
                                                <strong><i class="fas fa-lightbulb"></i> Features:</strong>
                                                Bash functions, HTTP status checking, JSON parsing, error handling, automation ready
                                            </div>
                                        </div>

                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
                                            <pre><code class="language-bash" data-include-snippet="/static/code-snippets/curl-examples.sh">
#!/bin/bash
# Hello Cyril API - cURL Examples

API_KEY="hc_your_api_key_here"
BASE_URL="https://hellocyril.co.za/api/subscription"

# Get crime reports
curl -H "X-API-Key: $API_KEY" \
     "$BASE_URL/reports?category=crime&limit=10"

# Get reports with date range
curl -H "X-API-Key: $API_KEY" \
     "$BASE_URL/reports?start_date=2024-01-01T00:00:00Z&end_date=2024-12-31T23:59:59Z&limit=50"

# Get platform statistics
curl -H "X-API-Key: $API_KEY" \
     "$BASE_URL/stats?period=weekly"

# Get API usage
curl -H "X-API-Key: $API_KEY" \
     "$BASE_URL/usage"

# Subscribe to webhooks (Premium/Enterprise only)
curl -X POST \
     -H "X-API-Key: $API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "webhook_url": "https://your-app.com/webhook",
       "alert_types": ["crime", "ems"],
       "geographic_filter": {
         "center": {"latitude": -25.7461, "longitude": 28.1881},
         "radius_km": 10
       }
     }' \
     "$BASE_URL/webhooks/subscribe"

# Test webhook delivery
curl -X POST \
     -H "X-API-Key: $API_KEY" \
     "$BASE_URL/webhooks/test"

# Get webhook status
curl -H "X-API-Key: $API_KEY" \
     "$BASE_URL/webhooks/status"

# Error handling example
response=$(curl -s -w "HTTPSTATUS:%{http_code}" \
               -H "X-API-Key: $API_KEY" \
               "$BASE_URL/reports")

http_code=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
response_body=$(echo "$response" | sed -e 's/HTTPSTATUS:.*//g')

if [ "$http_code" -eq 200 ]; then
    echo "✅ Success: $response_body"
else
    echo "❌ Error ($http_code): $response_body"
fi
                                            </code></pre>
                                        </div>

                                        <div class="alert-info">
                                            <strong><i class="fas fa-download"></i> Full Script:</strong>
                                            <a href="/static/code-snippets/curl-examples.sh" target="_blank" class="btn-hello-cyril ms-2">Download Complete cURL Script</a>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- API Endpoints -->
                            <section id="endpoints">
                                <h2><i class="fas fa-list"></i> API Endpoints</h2>

                                <!-- Reports Endpoint -->
                                <div class="endpoint-card">
                                    <div class="endpoint-header">
                                        <span class="method-badge method-get">GET</span>
                                        <strong>/reports</strong>
                                        <span class="text-muted">- Get crime, EMS, and infrastructure reports</span>
                                    </div>
                                    <div class="endpoint-body">
                                        <h5>Parameters</h5>
                                        <ul>
                                            <li><code>category</code> (optional): Filter by category (crime, ems, infrastructure)</li>
                                            <li><code>start_date</code> (optional): Start date in ISO format</li>
                                            <li><code>end_date</code> (optional): End date in ISO format</li>
                                            <li><code>limit</code> (optional): Number of results (max 1000, default 100)</li>
                                            <li><code>offset</code> (optional): Pagination offset (default 0)</li>
                                        </ul>

                                        <h5>Example Response</h5>
                                        <div class="code-block pb-4">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "reports": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "category": "crime",
      "description": "Suspicious activity reported",
      "location": {
        "latitude": -25.7461,
        "longitude": 28.1881
      },
      "timestamp": "2024-01-01T12:00:00Z",
      "verified": true
    }
  ],
  "total": 1,
  "offset": 0,
  "limit": 100
}
                                        </div>
                                    </div>
                                </div>

                                <!-- Stats Endpoint -->
                                <div class="endpoint-card">
                                    <div class="endpoint-header">
                                        <span class="method-badge method-get">GET</span>
                                        <strong>/stats</strong>
                                        <span class="text-muted">- Get platform statistics</span>
                                    </div>
                                    <div class="endpoint-body">
                                        <h5>Parameters</h5>
                                        <ul>
                                            <li><code>period</code> (optional): Time period (overall, daily, weekly, monthly)</li>
                                        </ul>

                                        <h5>Example Response</h5>
                                        <div class="code-block pb-4">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "stats": {
    "total_reports": 1250,
    "crime_reports": 800,
    "ems_reports": 300,
    "infrastructure_reports": 150,
    "verified_reports": 1100
  },
  "period": "overall",
  "timestamp": "2024-01-01T12:00:00Z"
}
                                        </div>
                                    </div>
                                </div>

                                <!-- WebSocket Endpoint -->
                                <div class="endpoint-card">
                                    <div class="endpoint-header">
                                        <span class="method-badge method-ws">WS</span>
                                        <strong>/ws</strong>
                                        <span class="text-muted">- Real-time alerts via WebSocket</span>
                                    </div>
                                    <div class="endpoint-body">
                                        <h5>Connection</h5>
                                        <div class="code-block pb-4">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
wss://hellocyril.co.za/api/subscription/ws?api_key=hc_your_api_key_here
                                        </div>

                                        <h5>Message Types</h5>
                                        <ul>
                                            <li><strong>connection</strong>: Welcome message when connected</li>
                                            <li><strong>alert</strong>: New report alert</li>
                                            <li><strong>ping/pong</strong>: Keep-alive messages</li>
                                        </ul>

                                        <h5>Example Alert Message</h5>
                                        <div class="code-block pb-4">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "type": "alert",
  "data": {
    "report_id": "123e4567-e89b-12d3-a456-426614174000",
    "category": "crime",
    "description": "Break-in reported",
    "location": {
      "latitude": -25.7461,
      "longitude": 28.1881
    },
    "timestamp": "2024-01-01T12:00:00Z"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
                                        </div>
                                    </div>
                                </div>

                                <!-- Webhooks -->
                                <div class="endpoint-card">
                                    <div class="endpoint-header">
                                        <span class="method-badge method-post">POST</span>
                                        <strong>/webhooks/subscribe</strong>
                                        <span class="text-muted">- Subscribe to webhook notifications (Premium/Enterprise)</span>
                                    </div>
                                    <div class="endpoint-body">
                                        <h5>Request Body</h5>
                                        <div class="code-block pb-4">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "webhook_url": "https://your-app.com/webhook",
  "alert_types": ["crime", "ems"],
  "geographic_filter": {
    "center": {
      "latitude": -25.7461,
      "longitude": 28.1881
    },
    "radius_km": 10
  }
}
                                        </div>

                                        <h5>Webhook Payload</h5>
                                        <div class="code-block pb-4">
                                            <button class="copy-btn" onclick="copyCode(this)">Copy</button>
{
  "event_type": "new_report",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "report_id": "123e4567-e89b-12d3-a456-426614174000",
    "category": "crime",
    "description": "Suspicious activity",
    "location": {
      "latitude": -25.7461,
      "longitude": 28.1881
    },
    "verified": false
  }
}
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Rate Limits -->
                            <section id="rate-limits">
                                <h2><i class="fas fa-tachometer-alt"></i> Rate Limits & Pricing</h2>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="endpoint-card">
                                            <div class="endpoint-header">
                                                <h5>Basic Plan - R99/month</h5>
                                            </div>
                                            <div class="endpoint-body">
                                                <ul>
                                                    <li>10,000 requests/month</li>
                                                    <li>100 requests/minute</li>
                                                    <li>Reports API access</li>
                                                    <li>Basic statistics</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="endpoint-card">
                                            <div class="endpoint-header">
                                                <h5>Premium Plan - R299/month</h5>
                                            </div>
                                            <div class="endpoint-body">
                                                <ul>
                                                    <li>50,000 requests/month</li>
                                                    <li>500 requests/minute</li>
                                                    <li>Real-time WebSocket alerts</li>
                                                    <li>Advanced statistics</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="endpoint-card">
                                            <div class="endpoint-header">
                                                <h5>Enterprise Plan - R999/month</h5>
                                            </div>
                                            <div class="endpoint-body">
                                                <ul>
                                                    <li>200,000 requests/month</li>
                                                    <li>2,000 requests/minute</li>
                                                    <li>Webhook notifications</li>
                                                    <li>Custom geographic filters</li>
                                                    <li>Priority support & SLA</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Error Codes -->
                            <section id="errors">
                                <h2><i class="fas fa-exclamation-triangle"></i> Error Codes</h2>

                                <div class="endpoint-card">
                                    <div class="endpoint-body">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Code</th>
                                                    <th>Description</th>
                                                    <th>Solution</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><code>401</code></td>
                                                    <td>Unauthorized - Invalid API key</td>
                                                    <td>Check your API key and ensure it's included in the X-API-Key header</td>
                                                </tr>
                                                <tr>
                                                    <td><code>429</code></td>
                                                    <td>Too Many Requests - Rate limit exceeded</td>
                                                    <td>Reduce request frequency or upgrade your plan</td>
                                                </tr>
                                                <tr>
                                                    <td><code>403</code></td>
                                                    <td>Forbidden - Feature not available in your plan</td>
                                                    <td>Upgrade to Premium or Enterprise plan</td>
                                                </tr>
                                                <tr>
                                                    <td><code>400</code></td>
                                                    <td>Bad Request - Invalid parameters</td>
                                                    <td>Check your request parameters and format</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </section>

                            <!-- Support -->
                            <section id="support">
                                <h2><i class="fas fa-life-ring"></i> Support</h2>
                                <p>Need help with the API? We're here to assist you!</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-envelope"></i> Email Support</h5>
                                        <p>Get help via email for all subscription tiers.</p>
                                        <a href="/feedback" class="btn-hello-cyril">Contact Support</a>
                                    </div>

                                    <div class="col-md-6">
                                        <h5><i class="fas fa-key"></i> Lost API Key?</h5>
                                        <p>Retrieve your API key using your subscription details.</p>
                                        <a href="/api-key-retrieval" class="btn-hello-cyril">Retrieve API Key</a>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <!-- Prism.js for syntax highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/toolbar/prism-toolbar.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>

    <script>
        function copyCode(button) {
            // Get the code block content
            const codeBlock = button.parentElement;
            let codeText;

            // Check if it's a Prism code block
            const codeElement = codeBlock.querySelector('code');
            if (codeElement) {
                codeText = codeElement.textContent;
            } else {
                codeText = codeBlock.textContent.replace('Copy', '').trim();
            }

            // Copy to clipboard
            if (navigator.clipboard) {
                navigator.clipboard.writeText(codeText).then(function() {
                    showCopySuccess(button);
                }).catch(function(err) {
                    console.error('Could not copy text: ', err);
                    fallbackCopyTextToClipboard(codeText, button);
                });
            } else {
                fallbackCopyTextToClipboard(codeText, button);
            }
        }

        function showCopySuccess(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.style.background = '#28a745';

            setTimeout(function() {
                button.innerHTML = originalText;
                button.style.background = '#ee4d4d';
            }, 2000);
        }

        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess(button);
                }
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
                alert('Please manually copy the code');
            }

            document.body.removeChild(textArea);
        }

        // Smooth scrolling for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            // Add navigation menu
            const sections = [
                {id: 'quick-start', title: 'Quick Start'},
                {id: 'authentication', title: 'Authentication'},
                {id: 'examples', title: 'Code Examples'},
                {id: 'endpoints', title: 'API Endpoints'},
                {id: 'rate-limits', title: 'Rate Limits'},
                {id: 'errors', title: 'Error Codes'},
                {id: 'support', title: 'Support'}
            ];

            // Create floating navigation
            const nav = document.createElement('div');
            nav.style.cssText = `
                position: fixed;
                top: 50%;
                right: 20px;
                transform: translateY(-50%);
                background: white;
                border: 2px solid #f27c7c;
                border-radius: 5px;
                padding: 15px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                z-index: 1000;
                max-height: 400px;
                overflow-y: auto;
            `;

            nav.innerHTML = '<h6 style="margin: 0 0 10px 0; color: #ee4d4d;">Navigation</h6>';

            sections.forEach(section => {
                const link = document.createElement('a');
                link.href = `#${section.id}`;
                link.textContent = section.title;
                link.style.cssText = `
                    display: block;
                    color: #ee4d4d;
                    text-decoration: none;
                    padding: 5px 0;
                    font-size: 14px;
                    border-bottom: 1px solid #f27c7c;
                `;
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById(section.id).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
                nav.appendChild(link);
            });

            document.body.appendChild(nav);

            // Hide navigation on mobile
            if (window.innerWidth < 768) {
                nav.style.display = 'none';
            }

            // Initialize Prism.js syntax highlighting
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();

                // Add line numbers to code blocks
                document.querySelectorAll('pre code').forEach(function(block) {
                    block.parentElement.classList.add('line-numbers');
                });

                // Re-highlight after tab switches
                document.querySelectorAll('[data-bs-toggle="tab"]').forEach(function(tab) {
                    tab.addEventListener('shown.bs.tab', function(e) {
                        // Add animation class to new tab content
                        const targetPane = document.querySelector(e.target.getAttribute('data-bs-target'));
                        if (targetPane) {
                            targetPane.style.opacity = '0';
                            targetPane.style.transform = 'translateY(10px)';

                            setTimeout(function() {
                                targetPane.style.transition = 'all 0.3s ease';
                                targetPane.style.opacity = '1';
                                targetPane.style.transform = 'translateY(0)';

                                // Re-highlight syntax
                                if (typeof Prism !== 'undefined') {
                                    Prism.highlightAll();
                                }
                            }, 50);
                        }

                        // Track tab usage (optional analytics)
                        const tabName = e.target.textContent.trim();
                        console.log(`User switched to ${tabName} tab`);
                    });
                });
            }

            // Add keyboard navigation for tabs
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    const tabs = document.querySelectorAll('#languageTabs .nav-link');
                    let currentIndex = -1;

                    // Find currently active tab
                    tabs.forEach((tab, index) => {
                        if (tab.classList.contains('active')) {
                            currentIndex = index;
                        }
                    });

                    let nextIndex = -1;

                    // Ctrl+1, Ctrl+2, Ctrl+3 for direct tab access
                    if (e.key >= '1' && e.key <= '3') {
                        nextIndex = parseInt(e.key) - 1;
                        e.preventDefault();
                    }
                    // Ctrl+Left/Right for tab navigation
                    else if (e.key === 'ArrowLeft' && currentIndex > 0) {
                        nextIndex = currentIndex - 1;
                        e.preventDefault();
                    }
                    else if (e.key === 'ArrowRight' && currentIndex < tabs.length - 1) {
                        nextIndex = currentIndex + 1;
                        e.preventDefault();
                    }

                    if (nextIndex >= 0 && nextIndex < tabs.length) {
                        tabs[nextIndex].click();
                    }
                }
            });

            // Load code snippets from external files
            document.querySelectorAll('[data-include-snippet]').forEach(function(element) {
                const snippetUrl = element.getAttribute('data-include-snippet');
                if (snippetUrl) {
                    fetch(snippetUrl)
                        .then(response => response.text())
                        .then(code => {
                            element.textContent = code;
                            if (typeof Prism !== 'undefined') {
                                Prism.highlightElement(element);
                            }
                        })
                        .catch(error => {
                            console.log('Could not load code snippet:', error);
                            // Keep the existing inline code as fallback
                        });
                }
            });
        });

        // Enhanced copy functionality for Prism code blocks
        document.addEventListener('DOMContentLoaded', function() {
            // Override Prism's copy button behavior
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('copy-to-clipboard-button')) {
                    const button = e.target;
                    const originalText = button.textContent;

                    button.textContent = '✅ Copied!';
                    button.style.background = '#28a745';

                    setTimeout(function() {
                        button.textContent = originalText;
                        button.style.background = '';
                    }, 2000);
                }
            });
        });
    </script>
{% endblock %}
