{% extends "base.html" %}

{% block title %}Retrieve API Key - Hello Cyril{% endblock %}

{% block extra_css %}
    <style>
        .retrieval-container {
            background: linear-gradient(135deg, #ee4d4d 0%, #f27c7c 100%);
            color: white;
            padding: 100px 0;
            min-height: 100vh;
        }
        
        .retrieval-card {
            background: white;
            color: #333;
            border-radius: 5px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            margin: 20px 0;
            font-family: "Dosis", arial, tahoma, verdana;
        }
        
        .form-control {
            border-radius: 5px;
            border: 2px solid #f27c7c;
            padding: 12px;
            font-family: "Dosis", arial, tahoma, verdana;
        }
        
        .form-control:focus {
            border-color: #ee4d4d;
            box-shadow: 0 0 0 0.2rem rgba(238, 77, 77, 0.25);
        }
        
        .btn-hello-cyril {
            background: #ee4d4d;
            border: 2px solid #ee4d4d;
            color: white;
            border-radius: 5px;
            text-transform: uppercase;
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 12px;
            box-shadow: 2px 2px 0 #f27c7c;
            transition: all 0.3s ease;
            position: relative;
            padding: 12px 30px;
        }
        
        .btn-hello-cyril:hover {
            background: #ee4d4d;
            border-color: #ee4d4d;
            color: white;
            box-shadow: none;
            top: 2px;
            left: 2px;
        }
        
        .btn-outline-hello-cyril {
            background: transparent;
            border: 2px solid #f27c7c;
            color: #f27c7c;
            border-radius: 5px;
            text-transform: uppercase;
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 12px;
            box-shadow: 2px 2px 0 #f27c7c;
            transition: all 0.3s ease;
            position: relative;
            padding: 12px 30px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-outline-hello-cyril:hover {
            background: transparent;
            border-color: #f27c7c;
            color: #f27c7c;
            box-shadow: none;
            top: 2px;
            left: 2px;
            text-decoration: none;
        }
        
        .api-key-result {
            background: #2b2e48;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            word-break: break-all;
            border: 2px solid #f27c7c;
            margin: 20px 0;
            display: none;
        }
        
        .info-box {
            background: #f8f9fa;
            border-left: 4px solid #ee4d4d;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .retrieval-icon {
            color: #ee4d4d;
            font-size: 3rem;
            margin-bottom: 20px;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="retrieval-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="retrieval-card text-center">
                        <i class="fas fa-key retrieval-icon"></i>
                        <h1>Retrieve Your API Key</h1>
                        <p class="lead">Enter your subscription details to retrieve your API key</p>
                        
                        <form id="retrievalForm" class="text-left">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" required>
                                <small class="form-text text-muted">The email address used for your subscription</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="contactName" class="form-label">Contact Name</label>
                                <input type="text" class="form-control" id="contactName" required>
                                <small class="form-text text-muted">The exact contact name used for your subscription</small>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-hello-cyril">
                                    <i class="fas fa-search"></i> Retrieve API Key
                                </button>
                            </div>
                        </form>
                        
                        <div id="resultContainer" style="display: none;">
                            <div class="info-box text-left">
                                <h5><i class="fas fa-check-circle text-success"></i> API Key Found!</h5>
                                <p>Here's your API key:</p>
                                
                                <div class="api-key-result" id="apiKeyResult">
                                    <span id="retrievedApiKey"></span>
                                </div>
                                
                                <button class="btn btn-hello-cyril" onclick="copyRetrievedKey()">
                                    <i class="fas fa-copy"></i> Copy API Key
                                </button>
                                
                                <div class="mt-3">
                                    <p><strong>Subscription:</strong> <span id="subscriptionTier"></span></p>
                                    <p><strong>Monthly Limit:</strong> <span id="monthlyLimit"></span> requests</p>
                                    <p><strong>Expires:</strong> <span id="expiresAt"></span></p>
                                </div>
                            </div>
                        </div>
                        
                        <div id="errorContainer" style="display: none;">
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span id="errorMessage"></span>
                            </div>
                        </div>
                        
                        <div class="info-box text-left">
                            <h5><i class="fas fa-info-circle"></i> Need Help?</h5>
                            <p>If you can't retrieve your API key:</p>
                            <ul>
                                <li>Make sure you're using the exact email and name from your subscription</li>
                                <li>Check that your subscription payment was successful</li>
                                <li>Contact support if you continue having issues</li>
                            </ul>
                        </div>
                        
                        <div class="mt-4">
                            <a href="/subscription" class="btn-outline-hello-cyril me-3">View Plans</a>
                            <a href="/" class="btn-outline-hello-cyril">Back to Home</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        document.getElementById('retrievalForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const contactName = document.getElementById('contactName').value;
            
            // Hide previous results
            document.getElementById('resultContainer').style.display = 'none';
            document.getElementById('errorContainer').style.display = 'none';
            
            // Show loading state
            const submitButton = document.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Retrieving...';
            submitButton.disabled = true;
            
            try {
                const response = await fetch('/api/payments/subscription/retrieve-api-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        contact_name: contactName
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // Show success result
                    document.getElementById('retrievedApiKey').textContent = result.api_key;
                    document.getElementById('subscriptionTier').textContent = result.subscription_tier.charAt(0).toUpperCase() + result.subscription_tier.slice(1);
                    document.getElementById('monthlyLimit').textContent = result.monthly_limit.toLocaleString();
                    document.getElementById('expiresAt').textContent = result.expires_at;
                    document.getElementById('resultContainer').style.display = 'block';
                } else {
                    // Show error
                    document.getElementById('errorMessage').textContent = result.detail || 'Failed to retrieve API key';
                    document.getElementById('errorContainer').style.display = 'block';
                }
                
            } catch (error) {
                document.getElementById('errorMessage').textContent = 'Network error. Please try again.';
                document.getElementById('errorContainer').style.display = 'block';
            }
            
            // Reset button
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
        
        function copyRetrievedKey() {
            const apiKeyText = document.getElementById('retrievedApiKey').textContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(apiKeyText).then(function() {
                    showCopySuccess();
                }).catch(function(err) {
                    console.error('Could not copy text: ', err);
                    fallbackCopyTextToClipboard(apiKeyText);
                });
            } else {
                fallbackCopyTextToClipboard(apiKeyText);
            }
        }
        
        function showCopySuccess() {
            const button = document.querySelector('button[onclick="copyRetrievedKey()"]');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Copied!';
            button.style.background = '#28a745';
            
            setTimeout(function() {
                button.innerHTML = originalText;
                button.style.background = '#ee4d4d';
            }, 2000);
        }
        
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess();
                }
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
                alert('Please manually copy the API key');
            }
            
            document.body.removeChild(textArea);
        }
    </script>
{% endblock %}
