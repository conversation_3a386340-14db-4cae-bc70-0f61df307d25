<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}Hello Cyril - Community Safety Reporting{% endblock %}</title>

  <!-- Favicon -->
  <link rel="icon" href="{{ url_for('static', path='img/lowhellocyril.jpg') }}" type="image/jpeg">

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />

  <!-- Leaflet MarkerCluster CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />

  <!-- Leaflet Draw CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css" />

  <!-- Google Fonts -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Dosis:300,400,500,600,700">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="{{ url_for('static', path='/css/style.css') }}">

  {% block extra_css %}{% endblock %}
</head>
<body>
  <header>
    <div class="container">
      <a href="/easteregg" class="logo">
        <img src="{{ url_for('static', path='img/lowhellocyril.jpg') }}" style="height: 50px; width: 50px; border-radius: 12px;">
        HELLO<span>CYRIL</span>
      </a>
      <section class="social">
        <a href="/" class="btn">Home</a>
        <a href="/map" class="btn">Live Report Map</a>
        <a href="/messages" class="btn">Latest Reports</a>
        <a href="https://wa.me/{{ whatsapp_number }}" class="btn" target="_blank">Add to WhatsApp</a>
        <button class="btn" onclick="openDonationModal()">❤️ Donate</button>
      </section>
    </div>
  </header>

  <div class="container">
    {% block content %}{% endblock %}
  </div>

  <!-- Donation Modal -->
  <div class="modal fade" id="donationModal" tabindex="-1">
      <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
              <div class="modal-header">
                  <h5 class="modal-title">❤️ Support Hello Cyril</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                  <div class="text-center mb-4">
                      <h4>Help Us Keep Communities Safe</h4>
                      <p class="text-muted">Your donation helps us:</p>
                  </div>

                  <div class="row mb-4">
                      <div class="col-6 text-center">
                          <div class="metric-icon">📱</div>
                          <strong>Send Alerts</strong>
                          <br><small>WhatsApp notifications to community groups</small>
                      </div>
                      <div class="col-6 text-center">
                          <div class="metric-icon">🛡️</div>
                          <strong>Improve Safety</strong>
                          <br><small>Better incident tracking and response</small>
                      </div>
                  </div>

                  <div class="donation-options">
                      <h6>Choose Donation Amount:</h6>
                      <div class="row mb-3">
                          <div class="col-4">
                              <button class="btn btn-outline-primary w-100" onclick="selectAmount(50)">R 50</button>
                          </div>
                          <div class="col-4">
                              <button class="btn btn-outline-primary w-100" onclick="selectAmount(100)">R 100</button>
                          </div>
                          <div class="col-4">
                              <button class="btn btn-outline-primary w-100" onclick="selectAmount(250)">R 250</button>
                          </div>
                      </div>
                      <div class="mb-3">
                          <input type="number" class="form-control" id="custom-amount" placeholder="Custom amount (R)" min="2">
                      </div>
                  </div>

                  <div class="payment-methods">
                      <h6>Payment Methods:</h6>
                      <div class="row">
                          <div class="col-12">
                              <button class="btn btn-primary w-100 mb-2" onclick="donateViaYoco()">
                                  <img src="{{ url_for('static', path='img/yoco.png') }}" alt="Yoco" style="height: 20px; margin-right: 8px;">🏦  Yoco
                              </button>
                          </div>
                      </div>
                  </div>

                  <div class="mt-3 text-center">
                      <small class="text-muted">
                          All donations are secure and help maintain the Hello Cyril platform.
                          <br>Thank you for supporting community safety! 🙏
                      </small>
                  </div>
              </div>
          </div>
      </div>
  </div>

  <!-- Leaflet JS -->
  <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>

  <!-- Leaflet MarkerCluster JS -->
  <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

  <!-- Leaflet Draw JS -->
  <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>

  <!-- jQuery (needed for datepicker) -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Custom JS -->
  <script src="{{ url_for('static', path='/js/script.js') }}"></script>

  <!-- Analytics Tracking -->
  <script src="{{ url_for('static', path='/js/analytics.js') }}"></script>

  <script>
    // Donation functionality (moved from stats.html)
    function openDonationModal() {
        const modal = new bootstrap.Modal(document.getElementById('donationModal'));
        modal.show();
    }

    function selectAmount(amount) {
        document.getElementById('custom-amount').value = amount;
        // Update button states if needed (visual feedback)
        document.querySelectorAll('.donation-options .btn-outline-primary').forEach(btn => {
            btn.classList.remove('btn-primary'); // Ensure it's not primary
            btn.classList.add('btn-outline-primary'); // Keep it outline
        });
        // Highlight the selected button
        event.target.classList.remove('btn-outline-primary');
        event.target.classList.add('btn-primary');
    }

    async function donateViaYoco() {
        const amountInput = document.getElementById('custom-amount');
        const amount = amountInput.value;
        if (!amount || parseFloat(amount) < 20) {
            alert('Please select or enter a donation amount of at least R20.00');
            return;
        }

        try {
            // Track donation initiation
            if (window.helloCyrilAnalytics) {
                await window.helloCyrilAnalytics.trackDonationIntent();
            }

            const response = await fetch('/api/payments/donate/yoco/create-checkout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ amount: parseFloat(amount) })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Yoco API Error: ${response.status}`);
            }

            const checkoutData = await response.json();
            if (checkoutData.redirectUrl) {
                window.location.href = checkoutData.redirectUrl;
            } else {
                alert('Could not initiate Yoco payment. Missing redirect URL.');
            }
        } catch (error) {
            console.error('Yoco donation error:', error);
            alert(`Error initiating Yoco payment: ${error.message}. Please try again.`);
        }
    }
  </script>

  {% block extra_js %}{% endblock %}
</body>
</html>
