{% extends "base.html" %}

{% block title %}Map View{% endblock %}

{% block extra_css %}
<style>
    /* Basic styling for the map container */
    #map {
        height: 600px !important;
        width: 100% !important;
        border-radius: 5px;
        z-index: 1;
        border: 2px solid #ddd;
    }

    /* Styling for the container */
    .map-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    /* Legend styling */
    .legend-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .legend-item {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .legend-color {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-right: 10px;
        border-radius: 50%;
    }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">GROUP AREA MAP</h1>

<div class="container">
    <div class="intro-section mb-5">
        <div class="card">
            <div class="card-body">
            <p class="logo">
                <span>Hello Cyril </span> is a community-driven safety reporting platform designed to keep 
                residents informed and empowered to take action. Group Admins can register their phone number
                in our database to receive real-time notifications.
            </p>
            <p>
                Our WhatsApp Business API incurs a cost of $0.02 (R0.52) per message. Larger group areas result
                in higher costs, so please create group areas thoughtfully, including only essential regions.
                We are committed to keeping our website and alerts free and ad-free for all users, but this relies
                on the support of our donations.
            </p>
            <p>
                Groups covering excessively large areas may be deactivated. For broader coverage, we recommend using
                our API subscription instead of the group function.
            </p>

            <div class="row mt-1">
                <div class="col-md-4 mb-4">
                <a href="/subscription" class="btn w-100">
                    Subscription
                </a>
                </div>
                <div class="col-md-4 mb-4">
                <a href="https://wa.me/{{ whatsapp_number }}" class="btn w-100" target="_blank">
                    Add to WhatsApp
                </a>
                </div>
                <div class="col-md-4 mb-4">
                <button type="button" class="btn btn-outline-danger w-100" id="remove-data-btn">
                    Remove My Data
                </button>
                </div>
            </div>
            </div>
        </div>
    </div>

    <!-- Map Container -->
    <div class="row mb-3">
        <div class="col-md-8">
            <div class="map-container">
                <div id="map"></div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="legend-container">
                <div class="card-body">
                    <form id="group-form" action="/api/groups/" method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Group Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        <!-- Hidden admin contact field with default value -->
                        <input type="hidden" id="admin_contact" name="admin_contact" value="not supported">
                        <div class="mb-3">
                            <label for="admin_phone" class="form-label">Admin Phone Number</label>
                            <input type="tel" class="form-control" id="admin_phone" name="admin_phone" required
                                   placeholder="+27821234567" pattern="^\+27[0-9]{9}$">
                            <small>Phone number for receiving notifications (format: +27xxxxxxxxx)</small><br>
                            <small class="text-muted">This will be used for WhatsApp notifications only and kept private</small>
                        </div>

                        <!-- Hidden whatsapp_group_id field with default value -->
                        <input type="hidden" id="whatsapp_group_id" name="whatsapp_group_id" value="not supported">

                        <div class="mb-3">
                            <label for="notification_frequency" class="form-label">Notification Frequency</label>
                            <select class="form-select" id="notification_frequency" name="notification_frequency">
                                <option value="real_time">Real-time</option>
                                <option value="hourly">Hourly</option>
                                <option value="daily">Daily</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="categories" class="form-label">Categories of Interest</label>
                            <select class="form-select" id="categories" name="categories" multiple>
                                <option value="crime">Crime</option>
                                <option value="ems">EMS</option>
                                <option value="infrastructure">Infrastructure</option>
                            </select>
                            <small>Hold Ctrl/Cmd to select multiple</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Area</label>
                            <p>Draw a polygon or rectangle on the map to define the group's area.</p>
                            <input type="hidden" id="area-coordinates" name="area-coordinates">
                        </div>

                        <button type="submit" class="btn w-100">Create Group</button>
                    </form>
                </div>

                <!-- Remove Data Section -->
                <div class="card-body border-top mt-3 pt-3">
                    <h6 class="text-muted mb-3">Data Management</h6>
                    <button type="button" class="btn btn-outline-danger w-100" id="remove-data-btn">
                        🗑️ Remove My Data
                    </button>
                    <small class="text-muted d-block mt-2 text-center">
                        Remove your phone number from all community groups
                    </small>
                </div>

            </div>
        </div>

    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>

async function loadGroups() {
    try {
        const response = await fetch('/api/groups/?active_only=true');
        if (!response.ok) {
            throw new Error(`Failed to fetch groups: ${response.statusText}`);
        }

        const groups = await response.json();
        addGroupPolygons(groups);
    } catch (error) {
        console.error('Error fetching groups:', error);
    }
}

// Function to generate a random color
function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
}

// Add group polygons to the map
function addGroupPolygons(groups) {
    for (const group of groups) {
        const coordinates = group.area_coordinates;

        if (coordinates && coordinates.length > 2) { // Ensure valid polygon coordinates
            const polygon = L.polygon(
                coordinates.map(coord => [coord[1], coord[0]]), // Convert [lng, lat] to [lat, lng]
                {
                    color: getRandomColor(), // Use a random color for each polygon
                    weight: 2,
                    fillOpacity: 0.1
                }
            ).addTo(map);

            // Bind a popup with group details
            polygon.bindPopup(`
                <strong>${group.name}</strong><br>
                ${group.description || 'No description'}<br>
                <strong>Categories:</strong> ${group.categories.length > 0 ? group.categories.join(', ') : 'None'}<br>
                <strong>Notification Frequency:</strong> ${group.notification_frequency}<br>
            `);

        } else {
            console.warn('Invalid area coordinates for group:', group);
        }
    }
}

// Initialize the map when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if Leaflet is available
    if (typeof L === 'undefined') {
        console.error("Leaflet is not loaded!");
        return;
    }

    // Check if map element exists
    const mapElement = document.getElementById('map');
    if (!mapElement) {
        console.error("Map element not found!");
        return;
    }

    try {
        // Create the map centered on South Africa
        map = L.map('map').setView([-29.5595, 22.9375], 5);

        // Add the tile layer (OpenStreetMap)
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add Leaflet.draw controls
        const drawControl = new L.Control.Draw({
            draw: {
                polyline: false,
                circle: false,
                marker: false,
                circlemarker: false,
                rectangle: true, // Enable rectangle drawing
                polygon: true    // Enable polygon drawing
            },
            edit: {
                featureGroup: new L.FeatureGroup().addTo(map) // Layer for drawn items
            }
        });
        map.addControl(drawControl);

        // Handle the creation of new shapes
        map.on(L.Draw.Event.CREATED, function(event) {
            const layer = event.layer;
            const type = event.layerType;

            if (type === 'polygon' || type === 'rectangle') {
                const coordinates = layer.getLatLngs();
                console.log('Drawn shape coordinates:', coordinates);

                // Save coordinates to the hidden input field
                document.getElementById('area-coordinates').value = JSON.stringify(coordinates);
            }

            // Add the drawn layer to the map
            drawControl.options.edit.featureGroup.addLayer(layer);
        });

        // Force map to resize after a short delay
        setTimeout(() => {
            map.invalidateSize();
        }, 500);

        // Load current reports and groups
        loadGroups();   // Load group polygons

    } catch (error) {
        console.error("Error creating map:", error);
    }
});

</script>

<script>
// Phone number formatting function
function formatPhoneNumber(input) {
    let value = input.value.replace(/\D/g, ''); // Remove all non-digits

    // If it starts with 0, remove it and add 27
    if (value.startsWith('0')) {
        value = '27' + value.substring(1);
    }

    // If it doesn't start with 27, add it
    if (!value.startsWith('27')) {
        value = '27' + value;
    }

    // Limit to 11 digits (27 + 9 digits)
    if (value.length > 11) {
        value = value.substring(0, 11);
    }

    // Add the + prefix
    input.value = '+' + value;
}

// Add phone number formatting on input
document.addEventListener('DOMContentLoaded', function() {
    const adminPhoneInput = document.getElementById('admin_phone');
    if (adminPhoneInput) {
        adminPhoneInput.addEventListener('input', function(e) {
            formatPhoneNumber(e.target);
        });
    }
});

// Validate phone number format
function validatePhoneNumber(phone) {
    const phoneRegex = /^\+27[0-9]{9}$/;
    return phoneRegex.test(phone);
}

document.getElementById('group-form').addEventListener('submit', async function(event) {
    event.preventDefault(); // Prevent the default form submission

    const form = event.target;
    const formData = new FormData(form);

    // Validate phone number
    const adminPhone = formData.get('admin_phone');
    if (adminPhone && !validatePhoneNumber(adminPhone)) {
        alert('Please enter a valid South African phone number in the format +27xxxxxxxxx');
        return;
    }

    // Convert form data to JSON
    const rawCoordinates = JSON.parse(formData.get('area-coordinates')); // Parse coordinates
    const areaCoordinates = rawCoordinates[0].map(coord => [coord.lng, coord.lat]); // Convert to [lng, lat]

    const data = {
        name: formData.get('name'),
        description: formData.get('description'),
        admin_contact: formData.get('admin_contact'),
        whatsapp_group_id: formData.get('whatsapp_group_id'),
        notification_frequency: formData.get('notification_frequency'),
        categories: Array.from(formData.getAll('categories')), // Handle multiple select
        area_coordinates: areaCoordinates, // Use transformed coordinates
        admin_phone: adminPhone // Add the admin phone number
    };

    console.log('Payload:', data);
    try {
        const response = await fetch(form.action, {
            method: form.method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`Failed to create group: ${response.statusText}`);
        }

        const result = await response.json();
        alert('Group created successfully!');
        console.log('Created group:', result);

        // Optionally, reload the page or reset the form
        form.reset();
    } catch (error) {
        console.error('Error creating group:', error);
        alert('Failed to create group. Please try again.');
    }
});

// Remove data functionality
document.getElementById("remove-data-btn").addEventListener("click", function() {
    showRemoveDataModal();
});

function showRemoveDataModal() {
    const removeModal = document.createElement("div");
    removeModal.className = "modal fade";
    removeModal.id = "removeDataModal";
    removeModal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">🗑️ Remove My Data</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>⚠️ Warning:</strong> This will permanently remove your phone number from all community groups in our database.
                    </div>

                    <form id="remove-data-form">
                        <div class="mb-3">
                            <label for="phone-to-remove" class="form-label">Your Phone Number</label>
                            <input type="tel" class="form-control" id="phone-to-remove"
                                   placeholder="+27821234567" pattern="^\\+27[0-9]{9}$" required>
                            <small class="text-muted">Enter the phone number you want to remove (format: +27xxxxxxxxx)</small>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirm-removal" required>
                                <label class="form-check-label" for="confirm-removal">
                                    I understand this action cannot be undone
                                </label>
                            </div>
                        </div>
                    </form>

                    <div id="removal-result" class="mt-3"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirm-remove-btn">Remove My Data</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(removeModal);
    const modal = new bootstrap.Modal(removeModal);
    modal.show();

    // Add phone number formatting
    const phoneInput = removeModal.querySelector('#phone-to-remove');
    phoneInput.addEventListener('input', function(e) {
        formatPhoneNumber(e.target);
    });

    // Handle removal confirmation
    removeModal.querySelector('#confirm-remove-btn').addEventListener('click', function() {
        removeUserData(removeModal);
    });

    // Clean up modal when hidden
    removeModal.addEventListener('hidden.bs.modal', function () {
        document.body.removeChild(removeModal);
    });
}

async function removeUserData(modal) {
    const phoneInput = modal.querySelector('#phone-to-remove');
    const confirmCheckbox = modal.querySelector('#confirm-removal');
    const removeBtn = modal.querySelector('#confirm-remove-btn');
    const resultDiv = modal.querySelector('#removal-result');

    // Validate form
    if (!phoneInput.value || !confirmCheckbox.checked) {
        resultDiv.innerHTML = '<div class="alert alert-danger">Please fill in all fields and confirm the removal.</div>';
        return;
    }

    // Validate phone number format
    if (!validatePhoneNumber(phoneInput.value)) {
        resultDiv.innerHTML = '<div class="alert alert-danger">Please enter a valid South African phone number (+27xxxxxxxxx).</div>';
        return;
    }

    // Show loading state
    removeBtn.textContent = 'Removing...';
    removeBtn.disabled = true;
    resultDiv.innerHTML = '<div class="alert alert-info">Searching for your data...</div>';

    try {
        const response = await fetch('/api/groups/remove-by-phone', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                phone_number: phoneInput.value
            })
        });

        const data = await response.json();

        if (response.ok) {
            if (data.removed_count > 0) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        ✅ Successfully removed your phone number from ${data.removed_count} community group(s).
                        <br><small>Groups affected: ${data.groups_removed.join(', ')}</small>
                    </div>
                `;

                // Disable form after successful removal
                phoneInput.disabled = true;
                confirmCheckbox.disabled = true;
                removeBtn.style.display = 'none';

                // Reload groups on the map to reflect changes
                setTimeout(() => {
                    location.reload();
                }, 2000);

            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-warning">
                        ℹ️ No community groups found with your phone number.
                        <br><small>Your phone number may not be in our database or may have been removed already.</small>
                    </div>
                `;
            }
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    ❌ Error: ${data.detail || 'Failed to remove data. Please try again.'}
                </div>
            `;
        }

    } catch (error) {
        console.error('Error removing data:', error);
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                ❌ Network error: Could not connect to server. Please try again later.
            </div>
        `;
    } finally {
        // Reset button state
        removeBtn.textContent = 'Remove My Data';
        removeBtn.disabled = false;
    }
}

</script>
{% endblock %}
