{% extends "base.html" %}

{% block extra_css %}
<style>
  .intro-section .card {
    border: none;
    border-radius: 5px;
    box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
  }

  .intro-section .card-header {
    background-color: #ee4d4d;
    color: white;
    border-radius: 3px 3px 0 0 !important;
    font-family: "Dosis", arial, tahoma, verdana;
    font-weight: 300;
  }

  .intro-section .btn {
    background-color: transparent;
    border: 2px solid #f27c7c;
    color: #f27c7c;
    border-radius: 5px;
    text-transform: uppercase;
    font-family: "Dosis", arial, tahoma, verdana;
    box-shadow: 2px 2px 0 #f27c7c;
    transition: all 0.3s ease;
  }

  .intro-section .btn:hover {
    background-color: transparent;
    border-color: #f27c7c;
    color: #f27c7c;
    box-shadow: none;
    top: 2px;
    left: 2px;
  }

  .intro-section .btn i {
    margin-right: 5px;
  }

  .intro-section p {
    font-family: "Dosis", arial, tahoma, verdana;
    font-size: 1.1em;
  }

  ul {
    font-family: "Dosis", arial, tahoma, verdana;
  }

  code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    color: #ee4d4d;
  }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">COMMUNITY SAFETY REPORTING SYSTEM</h1>


<div class="intro-section mb-5">
  <div class="card">
    <div class="card-header">
      <h2 class="h4 mb-0">ABOUT HELLO CYRIL</h2>
    </div>
    <div class="card-body">
      <p class="logo">
        <span>Hello Cyril </span> is a community-powered safety reporting platform designed to help residents stay
        informed and take action. With just a few taps on WhatsApp, anyone can anonymously report
        incidents—like crime, EMS emergencies, or infrastructure issues—and have them shared on an
        interactive map for the community to see.
      </p>
      <p>
        Our mission is simple: use a tool everyone already has WhatsApp to build a safer, more connected neighborhood.
      </p>
      <p>
        There are no gatekeepers, no middlemen just fast, anonymous reporting and real-time alerts.
        Reports are automatically sent to nearby community groups, even if you’re not part of them,
        ensuring that no one is left out of the loop.
      </p>
      <p>
        Whether you're reporting an issue, checking local activity, or organizing your community group,
        Hello Cyril helps you stay aware and take action—together.
      </p>

      <div class="row mt-4">
        <div class="col-md-3 mb-4">
          <a href="/map" class="btn w-100">
            <i class="bi bi-map"></i> Live Map
          </a>
        </div>
        <div class="col-md-3 mb-4">
          <a href="/heatmap" class="btn w-100">
            <i class="bi bi-map"></i> Heat Map
          </a>
        </div>
        <div class="col-md-3 mb-4">
          <a href="/messages" class="btn w-100">
            <i class="bi bi-people"></i> Latest Reports
          </a>
        </div>
        <div class="col-md-3 mb-4">
          <a href="/report" class="btn w-100">
            <i class="bi bi-file-earmark-text"></i> Generate Report
          </a>
        </div>
      </div>

      <div class="row mt-1">
        <div class="col-md-4 mb-4">
          <a href="/groups" class="btn w-100">
            <i class="bi bi-people"></i> Community Groups
          </a>
        </div>
        <div class="col-md-4 mb-4">
          <a href="/feedback" class="btn w-100">
            <i class="bi bi-file-earmark-text"></i> Feedback
          </a>
        </div>
        <div class="col-md-4 mb-4">
          <a href="/stats" class="btn w-100">
            <i class="bi bi-file-earmark-text"></i> Statistics
          </a>
        </div>
      </div>

    </div>
  </div>
</div>

<div id="timeline">
  <div class="timeline-item">
    <div class="timeline-icon">
      <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
        width="21px" height="20px" viewBox="0 0 21 20" enable-background="new 0 0 21 20" xml:space="preserve">
        <g>
          <path fill="#FFFFFF" d="M17.92,3.065l-1.669-2.302c-0.336-0.464-0.87-0.75-1.479-0.755C14.732,0.008,7.653,0,7.653,0v5.6
            c0,0.096-0.047,0.185-0.127,0.237c-0.081,0.052-0.181,0.06-0.268,0.02l-1.413-0.64C5.773,5.183,5.69,5.183,5.617,5.215l-1.489,0.65
            c-0.087,0.038-0.19,0.029-0.271-0.023c-0.079-0.052-0.13-0.141-0.13-0.235V0H2.191C1.655,0,1.233,0.434,1.233,0.97
            c0,0,0.025,15.952,0.031,15.993c0.084,0.509,0.379,0.962,0.811,1.242l2.334,1.528C4.671,19.905,4.974,20,5.286,20h10.307
            c1.452,0,2.634-1.189,2.634-2.64V4.007C18.227,3.666,18.12,3.339,17.92,3.065z M16.42,17.36c0,0.464-0.361,0.833-0.827,0.833H5.341
            l-1.675-1.089h10.341c0.537,0,0.953-0.44,0.953-0.979V2.039l1.459,2.027V17.36L16.42,17.36z"/>
        </g>
      </svg>
    </div>
    <div class="timeline-content">
      <h2>REPORT INCIDENTS</h2>
      <p>
        Report crime, EMS services, or infrastructure issues anonymously via WhatsApp.
        Your reports help keep our community safe and informed.
      </p>
      <p>
        <strong>How it works:</strong> When you send a report, our system will:
      </p>
      <ul>
        <li>Generate a unique ID for your report</li>
        <li>Notify relevant community groups in the area</li>
        <li>Add the report to our interactive map</li>
        <li>Send you information about nearby incidents</li>
      </ul>
      <a href="https://wa.me/{{ whatsapp_number }}" class="btn" target="_blank">Add to WhatsApp</a>
    </div>
  </div>

  <div class="timeline-item">
    <div class="timeline-icon">
      <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
        width="21px" height="20px" viewBox="0 0 21 20" enable-background="new 0 0 21 20" xml:space="preserve">
        <g>
          <path fill="#FFFFFF" d="M17.92,3.065l-1.669-2.302c-0.336-0.464-0.87-0.75-1.479-0.755C14.732,0.008,7.653,0,7.653,0v5.6
            c0,0.096-0.047,0.185-0.127,0.237c-0.081,0.052-0.181,0.06-0.268,0.02l-1.413-0.64C5.773,5.183,5.69,5.183,5.617,5.215l-1.489,0.65
            c-0.087,0.038-0.19,0.029-0.271-0.023c-0.079-0.052-0.13-0.141-0.13-0.235V0H2.191C1.655,0,1.233,0.434,1.233,0.97
            c0,0,0.025,15.952,0.031,15.993c0.084,0.509,0.379,0.962,0.811,1.242l2.334,1.528C4.671,19.905,4.974,20,5.286,20h10.307
            c1.452,0,2.634-1.189,2.634-2.64V4.007C18.227,3.666,18.12,3.339,17.92,3.065z M16.42,17.36c0,0.464-0.361,0.833-0.827,0.833H5.341
            l-1.675-1.089h10.341c0.537,0,0.953-0.44,0.953-0.979V2.039l1.459,2.027V17.36L16.42,17.36z"/>
        </g>
      </svg>
    </div>
    <div class="timeline-content right">
      <h2>HOW TO REPORT</h2>
      <p>
        Send a message to our WhatsApp number with the following format:
        <br><br>
        <strong>#category</strong> (crime, ems, or infrastructure)<br>
        <strong>Description</strong> of the issue<br>
        <strong>Location:</strong> Share your location<br>
        <strong>Image:</strong> Attach an image (optional)
      </p>
      <p>
        <strong>Check report status:</strong> Send <code>#status REPORT_ID</code> to check the status of your report.
      </p>
      <div class="mt-3">
        <button type="button" class="btn" data-bs-toggle="modal" data-bs-target="#demoVideoModal">
          <i class="bi bi-play-circle"></i> Watch Demo
        </button>
      </div>
    </div>
  </div>

  <div class="timeline-item">
    <div class="timeline-icon">
      <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
        width="21px" height="20px" viewBox="0 0 21 20" enable-background="new 0 0 21 20" xml:space="preserve">
        <g>
          <path fill="#FFFFFF" d="M17.92,3.065l-1.669-2.302c-0.336-0.464-0.87-0.75-1.479-0.755C14.732,0.008,7.653,0,7.653,0v5.6
            c0,0.096-0.047,0.185-0.127,0.237c-0.081,0.052-0.181,0.06-0.268,0.02l-1.413-0.64C5.773,5.183,5.69,5.183,5.617,5.215l-1.489,0.65
            c-0.087,0.038-0.19,0.029-0.271-0.023c-0.079-0.052-0.13-0.141-0.13-0.235V0H2.191C1.655,0,1.233,0.434,1.233,0.97
            c0,0,0.025,15.952,0.031,15.993c0.084,0.509,0.379,0.962,0.811,1.242l2.334,1.528C4.671,19.905,4.974,20,5.286,20h10.307
            c1.452,0,2.634-1.189,2.634-2.64V4.007C18.227,3.666,18.12,3.339,17.92,3.065z M16.42,17.36c0,0.464-0.361,0.833-0.827,0.833H5.341
            l-1.675-1.089h10.341c0.537,0,0.953-0.44,0.953-0.979V2.039l1.459,2.027V17.36L16.42,17.36z"/>
        </g>
      </svg>
    </div>
    <div class="timeline-content">
      <h2>COMMUNITY GROUPS</h2>
      <p>
        Join or create community safety groups for your neighborhood. Groups receive notifications about incidents in their area based on their preferences.
      </p>
      <p>
        <strong>Group features:</strong>
      </p>
      <ul>
        <li>Define geographic boundaries for your group</li>
        <li>Choose notification frequency (real-time, hourly, daily)</li>
        <li>Select categories of interest (crime, EMS, infrastructure)</li>
        <li>Connect with WhatsApp groups for easy communication</li>
      </ul>
      <a href="/groups" class="btn">Manage Groups</a>
    </div>
  </div>

  <div class="timeline-item">
    <div class="timeline-icon">
      <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
        width="21px" height="20px" viewBox="0 0 21 20" enable-background="new 0 0 21 20" xml:space="preserve">
        <g>
          <path fill="#FFFFFF" d="M17.92,3.065l-1.669-2.302c-0.336-0.464-0.87-0.75-1.479-0.755C14.732,0.008,7.653,0,7.653,0v5.6
            c0,0.096-0.047,0.185-0.127,0.237c-0.081,0.052-0.181,0.06-0.268,0.02l-1.413-0.64C5.773,5.183,5.69,5.183,5.617,5.215l-1.489,0.65
            c-0.087,0.038-0.19,0.029-0.271-0.023c-0.079-0.052-0.13-0.141-0.13-0.235V0H2.191C1.655,0,1.233,0.434,1.233,0.97
            c0,0,0.025,15.952,0.031,15.993c0.084,0.509,0.379,0.962,0.811,1.242l2.334,1.528C4.671,19.905,4.974,20,5.286,20h10.307
            c1.452,0,2.634-1.189,2.634-2.64V4.007C18.227,3.666,18.12,3.339,17.92,3.065z M16.42,17.36c0,0.464-0.361,0.833-0.827,0.833H5.341
            l-1.675-1.089h10.341c0.537,0,0.953-0.44,0.953-0.979V2.039l1.459,2.027V17.36L16.42,17.36z"/>
        </g>
      </svg>
    </div>
    <div class="timeline-content right">
      <h2>INTERACTIVE MAP</h2>
      <p>
        View all reported incidents on our interactive map. Filter by category, date range, and verification status to see what's happening in your area.
      </p>
      <p>
        <strong>Map features:</strong>
      </p>
      <ul>
        <li>Color-coded incidents by category</li>
        <li>Date range filtering with calendar picker</li>
        <li>View detailed information about each incident</li>
        <li>See recent reports in a list view</li>
      </ul>
      <a href="/map" class="btn">View Live Map</a>
    </div>
  </div>

  <div class="timeline-item">
    <div class="timeline-icon">
      <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
        width="21px" height="20px" viewBox="0 0 21 20" enable-background="new 0 0 21 20" xml:space="preserve">
        <g>
          <path fill="#FFFFFF" d="M17.92,3.065l-1.669-2.302c-0.336-0.464-0.87-0.75-1.479-0.755C14.732,0.008,7.653,0,7.653,0v5.6
            c0,0.096-0.047,0.185-0.127,0.237c-0.081,0.052-0.181,0.06-0.268,0.02l-1.413-0.64C5.773,5.183,5.69,5.183,5.617,5.215l-1.489,0.65
            c-0.087,0.038-0.19,0.029-0.271-0.023c-0.079-0.052-0.13-0.141-0.13-0.235V0H2.191C1.655,0,1.233,0.434,1.233,0.97
            c0,0,0.025,15.952,0.031,15.993c0.084,0.509,0.379,0.962,0.811,1.242l2.334,1.528C4.671,19.905,4.974,20,5.286,20h10.307
            c1.452,0,2.634-1.189,2.634-2.64V4.007C18.227,3.666,18.12,3.339,17.92,3.065z M16.42,17.36c0,0.464-0.361,0.833-0.827,0.833H5.341
            l-1.675-1.089h10.341c0.537,0,0.953-0.44,0.953-0.979V2.039l1.459,2.027V17.36L16.42,17.36z"/>
        </g>
      </svg>
    </div>
    <div class="timeline-content">
      <h2>SAFETY REPORTS</h2>
      <p>
        Generate detailed safety reports for specific areas, categories, and date ranges. Perfect for community meetings, neighborhood watch groups, or personal safety awareness.
      </p>
      <p>
        <strong>Report features:</strong>
      </p>
      <ul>
        <li>Select an area on the map</li>
        <li>Choose incident categories to include</li>
        <li>Specify a date range</li>
        <li>Download as PDF for easy sharing</li>
      </ul>
      <a href="/report" class="btn">Generate Report</a>
    </div>
  </div>
</div>

<!-- Demo Video Modal -->
<div class="modal fade" id="demoVideoModal" tabindex="-1" aria-labelledby="demoVideoModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="demoVideoModalLabel">
          <i class="bi bi-play-circle"></i> Hello Cyril Demo
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="ratio ratio-16x9">
          <video id="demoVideo" controls poster="/static/img/demo-thumbnail.jpg">
            <source src="/static/vid/DEMO.mp4" type="video/mp4">
            <source src="/static/vid/DEMO.webm" type="video/webm">
            <!-- Fallback for YouTube or external video -->
            <iframe
              src="/static/vid/DEMO.mp4"
              title="Hello Cyril Demo Video"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowfullscreen>
            </iframe>
          </video>
        </div>
        <div class="mt-3">
          <p class="text-muted">
            <i class="bi bi-info-circle"></i>
            This demo shows how to report incidents using WhatsApp and view them in Google maps.
          </p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <a href="https://wa.me/{{ whatsapp_number }}" class="btn btn-primary" target="_blank">
          <i class="bi bi-whatsapp"></i> Start Reporting
        </a>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
  // Initialize map with reports data
  document.addEventListener('DOMContentLoaded', function() {
    initMap();
    loadReports();

    // Handle demo video modal
    const demoVideoModal = document.getElementById('demoVideoModal');
    const demoVideo = document.getElementById('demoVideo');

    if (demoVideoModal && demoVideo) {
      // Pause video when modal is closed
      demoVideoModal.addEventListener('hidden.bs.modal', function () {
        demoVideo.pause();
        demoVideo.currentTime = 0;
      });

      // Auto-play video when modal opens (if user allows)
      demoVideoModal.addEventListener('shown.bs.modal', function () {
        // Only auto-play if user has interacted with the page
        demoVideo.play().catch(function(error) {
          console.log('Auto-play prevented:', error);
        });
      });
    }
  });
</script>
{% endblock %}
