{% extends "base.html" %}

{% block title %}Generate Report{% endblock %}

{% block extra_css %}
<!-- Include jsPDF for PDF generation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<!-- Include Flatpickr for better date picking -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<style>
  .report-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .report-controls {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
  }

  .control-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;
  }

  .control-group {
    flex: 1;
    min-width: 200px;
  }

  .control-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .control-group select,
  .control-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .map-selection-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
  }

  #selection-map {
    height: 400px;
    width: 100%;
    border-radius: 5px;
  }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  .generate-btn {
    background-color: #ee4d4d;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
  }

  .generate-btn:hover {
    background-color: #d32f2f;
  }

  .reset-btn {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #333;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
  }

  .reset-btn:hover {
    background-color: #e0e0e0;
  }

  #report-preview {
    display: none;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 20px;
  }

  .report-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .report-header h2 {
    color: #ee4d4d;
    margin-bottom: 5px;
  }

  .report-header p {
    color: #666;
    margin: 5px 0;
  }

  #report-map {
    height: 400px;
    width: 100%;
    border-radius: 5px;
    margin-bottom: 20px;
  }

  .report-incidents {
    margin-top: 20px;
  }

  .report-incidents h3 {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }

  .incident-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .incident-card {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .incident-card h4 {
    color: #ee4d4d;
    margin-top: 0;
    margin-bottom: 10px;
  }

  .incident-meta {
    color: #888;
    font-size: 0.9em;
    margin-bottom: 10px;
  }

  .download-btn {
    background-color: #4CAF50;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
    margin-top: 20px;
  }

  .download-btn:hover {
    background-color: #388E3C;
  }

  .category-badge {
    border-radius: 20px;
    display: inline-block;
    font-size: 0.8em;
    margin-left: 10px;
    padding: 3px 10px;
    text-transform: uppercase;
  }

  .category-crime {
    background-color: #ffebee;
    color: #c62828;
  }

  .category-ems {
    background-color: #e3f2fd;
    color: #1565c0;
  }

  .category-infrastructure {
    background-color: #fff8e1;
    color: #ff8f00;
  }

  /* Flatpickr customization */
  .flatpickr-calendar {
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
  }

  .flatpickr-day.selected,
  .flatpickr-day.startRange,
  .flatpickr-day.endRange,
  .flatpickr-day.selected.inRange,
  .flatpickr-day.startRange.inRange,
  .flatpickr-day.endRange.inRange,
  .flatpickr-day.selected:focus,
  .flatpickr-day.startRange:focus,
  .flatpickr-day.endRange:focus,
  .flatpickr-day.selected:hover,
  .flatpickr-day.startRange:hover,
  .flatpickr-day.endRange:hover,
  .flatpickr-day.selected.prevMonthDay,
  .flatpickr-day.startRange.prevMonthDay,
  .flatpickr-day.endRange.prevMonthDay,
  .flatpickr-day.selected.nextMonthDay,
  .flatpickr-day.startRange.nextMonthDay,
  .flatpickr-day.endRange.nextMonthDay {
    background: #ee4d4d;
    border-color: #ee4d4d;
  }

  .flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
  .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
  .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
    box-shadow: -10px 0 0 #ee4d4d;
  }

  .flatpickr-months .flatpickr-month {
    background: #ee4d4d;
  }

  .flatpickr-months .flatpickr-prev-month,
  .flatpickr-months .flatpickr-next-month {
    color: rgba(255, 255, 255, 0.9);
    fill: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month .flatpickr-monthDropdown-months {
    background: #ee4d4d;
    color: white;
  }

  .flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
    background-color: #ee4d4d;
  }

  .flatpickr-current-month .numInputWrapper span.arrowUp:after {
    border-bottom-color: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month .numInputWrapper span.arrowDown:after {
    border-top-color: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month input.cur-year {
    color: white;
  }

  /* Print styles */
  @media print {
    body * {
      visibility: hidden;
    }
    #report-preview, #report-preview * {
      visibility: visible;
    }
    #report-preview {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
    }
    .download-btn {
      display: none;
    }
  }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">GENERATE INCIDENT REPORT</h1>

<div class="report-container">
  <div class="report-controls">
    <h2>Report Parameters</h2>
    <div class="control-row">
      <div class="control-group">
        <label for="category-filter">Category:</label>
        <select id="category-filter">
          <option value="all">All Categories</option>
          <option value="crime">Crime</option>
          <option value="ems">EMS</option>
          <option value="infrastructure">Infrastructure</option>
        </select>
      </div>
      <div class="control-group">
        <label for="start-date">Start Date:</label>
        <input type="text" id="start-date" placeholder="Select start date" readonly>
      </div>
      <div class="control-group">
        <label for="end-date">End Date:</label>
        <input type="text" id="end-date" placeholder="Select end date" readonly>
      </div>
    </div>
    <p>Select an area on the map below by clicking and dragging to create a rectangle:</p>
  </div>

  <div class="map-selection-container">
    <div id="selection-map"></div>
    <div class="action-buttons">
      <button class="reset-btn" id="reset-selection">Reset Selection</button>
      <button class="generate-btn" id="generate-report">Generate Report</button>
    </div>
  </div>

  <div id="report-preview">
    <div class="report-header">
      <h2>HELLO CYRIL - INCIDENT REPORT</h2>
      <p id="report-date-range">Date Range: <span id="date-range-text"></span></p>
      <p id="report-category">Category: <span id="category-text"></span></p>
      <p id="report-area">Area: <span id="area-text"></span></p>
    </div>

    <div id="report-map"></div>

    <div class="report-incidents">
      <h3>Incidents in Selected Area</h3>
      <div id="incident-count">Total: <span id="count-text">0</span> incidents found</div>
      <div class="incident-cards" id="incident-cards-container">
        <!-- Incident cards will be inserted here -->
      </div>
    </div>

    <button class="btn" id="download-pdf">Download as PDF</button>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  // Initialize variables
  let selectionMap, reportMap;
  let drawnRectangle = null;
  let selectedBounds = null;
  let filteredReports = [];
  let drawnItems; // Feature group for drawn items

  // Initialize maps and controls when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    initSelectionMap();
    initDatepickers();
    setupEventListeners();
  });

  // Initialize the selection map
  function initSelectionMap() {
    // Create the selection map centered on South Africa
    selectionMap = L.map('selection-map').setView([-30.5595, 22.9375], 5);

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(selectionMap);

    // Initialize a feature group for drawn items
    drawnItems = new L.FeatureGroup().addTo(selectionMap);

    // Add Leaflet.draw controls
    const drawControl = new L.Control.Draw({
      draw: {
        polyline: false,
        circle: false,
        marker: false,
        circlemarker: false,
        rectangle: {
          shapeOptions: {
            color: '#ee4d4d',
            weight: 2
          }
        },
        polygon: {
          shapeOptions: {
            color: '#ee4d4d',
            weight: 2
          }
        }
      },
      edit: {
        featureGroup: drawnItems // Layer for drawn items
      }
    });

    selectionMap.addControl(drawControl);

    // Handle the creation of new shapes
    selectionMap.on(L.Draw.Event.CREATED, function(event) {
      const layer = event.layer;
      const type = event.layerType;

      // Add the new layer to the FeatureGroup
      drawnItems.addLayer(layer);

      if (type === 'rectangle') {
        // Store the bounds for filtering
        selectedBounds = layer.getBounds();
        console.log('Rectangle bounds:', selectedBounds);
      } else if (type === 'polygon') {
        // For polygon, we need to calculate bounds from coordinates
        const coordinates = layer.getLatLngs();
        selectedBounds = layer.getBounds();
        console.log('Polygon coordinates:', coordinates);
        console.log('Polygon bounds:', selectedBounds);
      }
    });
  }

  // Initialize datepickers using Flatpickr
  function initDatepickers() {
    // Set default dates (last 7 days)
    const today = new Date();
    const weekAgo = new Date();
    weekAgo.setDate(today.getDate() - 7);

    // Format dates for display
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    // Initialize start date picker with Flatpickr
    flatpickr("#start-date", {
      dateFormat: "Y-m-d",
      defaultDate: weekAgo,
      maxDate: today,
      disableMobile: "true",
      onChange: function(selectedDates, dateStr) {
        // Update end date picker's min date
        endDatePicker.set('minDate', dateStr);
      }
    });

    // Initialize end date picker with Flatpickr
    const endDatePicker = flatpickr("#end-date", {
      dateFormat: "Y-m-d",
      defaultDate: today,
      maxDate: today,
      minDate: formatDate(weekAgo),
      disableMobile: "true"
    });
  }

  // Set up event listeners
  function setupEventListeners() {
    // Generate report button
    document.getElementById('generate-report').addEventListener('click', generateReport);

    // Reset selection button
    document.getElementById('reset-selection').addEventListener('click', resetSelection);

    // Download PDF button
    document.getElementById('download-pdf').addEventListener('click', downloadPDF);
  }

  // Generate the report based on selected parameters
  function generateReport() {
    // Check if an area has been selected
    if (!selectedBounds) {
      alert('Please select an area on the map first.');
      return;
    }

    // Get filter values
    const category = document.getElementById('category-filter').value;
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    // Calculate days from start date to today
    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();
    const daysDiff = Math.ceil((today - start) / (1000 * 60 * 60 * 24));

    // Fetch reports from API with proper query parameters
    fetch(`/api/reports/?start_date=${startDate}&end_date=${endDate}`)
      .then(response => response.json())
      .then(data => {
        // Filter reports by selected bounds and category
        filteredReports = filterReportsByBoundsAndCategory(data, selectedBounds, category, start, end);

        // Display the report
        displayReport(filteredReports, category, startDate, endDate);
      })
      .catch(error => {
        console.error('Error fetching reports:', error);
        alert('Error fetching reports. Please try again.');
      });
  }

  // Filter reports by bounds and category
  function filterReportsByBoundsAndCategory(reports, bounds, category, startDate, endDate) {
    return reports.filter(report => {
      // Check if report is within the selected bounds
      const reportLatLng = L.latLng(report.location.latitude, report.location.longitude);
      const isInBounds = bounds.contains(reportLatLng);

      // Check if report matches the selected category
      const matchesCategory = category === 'all' || report.category === category;

      // Check if report is within the date range
      const reportDate = new Date(report.timestamp);
      const isInDateRange = reportDate >= startDate && reportDate <= endDate;

      return isInBounds && matchesCategory && isInDateRange;
    });
  }

  // Display the generated report
  function displayReport(reports, category, startDate, endDate) {
    // Show the report preview
    document.getElementById('report-preview').style.display = 'block';

    // Update report header information
    document.getElementById('date-range-text').textContent = `${startDate} to ${endDate}`;
    document.getElementById('category-text').textContent = category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1);
    document.getElementById('area-text').textContent = `Selected Area (${selectedBounds.getSouth().toFixed(4)}, ${selectedBounds.getWest().toFixed(4)}) to (${selectedBounds.getNorth().toFixed(4)}, ${selectedBounds.getEast().toFixed(4)})`;
    document.getElementById('count-text').textContent = reports.length;

    // Initialize the report map
    if (reportMap) {
      reportMap.remove();
    }

    reportMap = L.map('report-map').fitBounds(selectedBounds);

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(reportMap);

    // Add the selection rectangle to the map
    L.rectangle(selectedBounds, {
      color: '#ee4d4d',
      weight: 2,
      fillOpacity: 0.1
    }).addTo(reportMap);

    // Add markers for each report
    reports.forEach(report => {
      const marker = L.marker([report.location.latitude, report.location.longitude]);
      marker.addTo(reportMap);
    });

    // Generate incident cards
    generateIncidentCards(reports);

    // Scroll to the report preview
    document.getElementById('report-preview').scrollIntoView({ behavior: 'smooth' });
  }

  // Generate incident cards for the report
  function generateIncidentCards(reports) {
    const container = document.getElementById('incident-cards-container');
    container.innerHTML = '';

    if (reports.length === 0) {
      container.innerHTML = '<p>No incidents found in the selected area and time period.</p>';
      return;
    }

    reports.forEach(report => {
      const categoryClass = `category-${report.category}`;
      const formattedDate = new Date(report.timestamp).toLocaleString();

      const card = document.createElement('div');
      card.className = 'incident-card';
      card.innerHTML = `
        <h4>
          Incident #${report.id.substring(0, 8)}
          <span class="category-badge ${categoryClass}">${report.category}</span>
        </h4>
        <div class="incident-meta">
          Reported: ${formattedDate}
        </div>
        <p>${report.description}</p>
        <div class="incident-location">
          Location: ${report.location.latitude.toFixed(4)}, ${report.location.longitude.toFixed(4)}
        </div>
      `;

      container.appendChild(card);
    });
  }

  // Reset the map selection
  function resetSelection() {
    // Clear all drawn items
    if (drawnItems) {
      drawnItems.clearLayers();
    }

    // Reset variables
    drawnRectangle = null;
    selectedBounds = null;

    // Reset the form
    document.getElementById('category-filter').value = 'all';

    // Hide the report preview
    document.getElementById('report-preview').style.display = 'none';
  }

  // Download the report as PDF
  function downloadPDF() {
    const { jsPDF } = window.jspdf;

    // Create a new PDF document
    const doc = new jsPDF('p', 'mm', 'a4');
    const reportElement = document.getElementById('report-preview');

    // Use html2canvas to capture the report as an image
    html2canvas(reportElement, {
      scale: 2,
      useCORS: true,
      logging: false
    }).then(canvas => {
      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = canvas.height * imgWidth / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Add the image to the PDF
      doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add new pages if the content is longer than one page
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        doc.addPage();
        doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Save the PDF
      doc.save('hello-cyril-report.pdf');
    });
  }
</script>
{% endblock %}
