{% extends "base.html" %}

{% block title %}Statistics{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .donation-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .donation-btn {
        background: rgba(255,255,255,0.2);
        border: 2px solid white;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .donation-btn:hover {
        background: white;
        color: #f5576c;
    }

    .metric-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .period-selector {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">HELLO CYRIL STATISTICS</h1>

<div class="container">
    <!-- Period Selector -->
    <div class="period-selector">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">📊 Platform Statistics</h5>
                <small class="text-muted">Real-time insights into Hello Cyril usage</small>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-danger active" id="overall-btn">Overall</button>
                    <button type="button" class="btn btn-outline-danger" id="monthly-btn">This Month</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Statistics Cards -->
    <div class="row" id="stats-container">
        <!-- Loading placeholder -->
        <div class="col-12 text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading statistics...</span>
            </div>
            <p class="mt-2">Loading statistics...</p>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="chart-container">
                <h5>📈 Reports by Category</h5>
                <canvas id="categoryChart" width="400" height="300"></canvas>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h5>📅 Reports Over Time</h5>
                <canvas id="timeChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Donation Section -->
    <div class="row mt-4">
        <div class="col-md-8">
            <div class="chart-container">
                <h5>💰 Financial Overview</h5>
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="metric-icon">💸</div>
                        <h4 id="total-cost">R 0.00</h4>
                        <small class="text-muted">Total SMS Cost</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="metric-icon">📱</div>
                        <h4 id="messages-sent">0</h4>
                        <small class="text-muted">Messages Sent</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="metric-icon">💵</div>
                        <h4 id="cost-per-message">R 0.00</h4>
                        <small class="text-muted">Cost per Message</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="donation-card">
                <div class="metric-icon">❤️</div>
                <h4>Support Hello Cyril</h4>
                <p class="mb-3">Help us keep the platform running and improve community safety across South Africa.</p>
                <button class="btn donation-btn" onclick="openDonationModal()">
                    💝 Donate Now
                </button>
                <div class="mt-3">
                    <small>Every donation helps us send more alerts and protect more communities</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Metrics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="chart-container">
                <h5>🌍 Geographic Distribution</h5>
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="metric-icon">🏘️</div>
                        <h4 id="active-areas">0</h4>
                        <small class="text-muted">Active Areas</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="metric-icon">⚡</div>
                        <h4 id="avg-response-time">0 min</h4>
                        <small class="text-muted">Avg Response Time</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="metric-icon">📊</div>
                        <h4 id="resolution-rate">0%</h4>
                        <small class="text-muted">Resolution Rate</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="metric-icon">🔥</div>
                        <h4 id="peak-hours">12:00</h4>
                        <small class="text-muted">Peak Activity</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Donation Modal -->
<div class="modal fade" id="donationModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">❤️ Support Hello Cyril</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h4>Help Us Keep Communities Safe</h4>
                    <p class="text-muted">Your donation helps us:</p>
                </div>

                <div class="row mb-4">
                    <div class="col-6 text-center">
                        <div class="metric-icon">📱</div>
                        <strong>Send Alerts</strong>
                        <br><small>WhatsApp notifications to community groups</small>
                    </div>
                    <div class="col-6 text-center">
                        <div class="metric-icon">🛡️</div>
                        <strong>Improve Safety</strong>
                        <br><small>Better incident tracking and response</small>
                    </div>
                </div>

                <div class="donation-options">
                    <h6>Choose Donation Amount:</h6>
                    <div class="row mb-3">
                        <div class="col-4">
                            <button class="btn btn-outline-primary w-100" onclick="selectAmount(50)">R 50</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-primary w-100" onclick="selectAmount(100)">R 100</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-outline-primary w-100" onclick="selectAmount(250)">R 250</button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <input type="number" class="form-control" id="custom-amount" placeholder="Custom amount (R)" min="10">
                    </div>
                </div>

                <div class="payment-methods">
                    <h6>Payment Methods:</h6>
                    <div class="row">
                        <div class="col-12">
                            <button class="btn btn-primary w-100 mb-2" onclick="donateViaYoco()">
                                <img src="/static/images/yoco_logo.png" alt="Yoco" style="height: 20px; margin-right: 8px;">🏦  Yoco
                            </button>
                        </div>
<!--
                        <div class="col-6">
                            <button class="btn btn-info w-100 mb-2" onclick="donateViaEFT()">
                                🏦 EFT Transfer
                            </button>
                        </div>
-->
                    </div>
                </div>

                <div class="mt-3 text-center">
                    <small class="text-muted">
                        All donations are secure and help maintain the Hello Cyril platform.
                        <br>Thank you for supporting community safety! 🙏
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Chart.js for statistics visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let currentPeriod = 'overall';
let categoryChart, timeChart;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
    initializeCharts();

    // Period selector handlers
    document.getElementById('overall-btn').addEventListener('click', function() {
        switchPeriod('overall');
    });

    document.getElementById('monthly-btn').addEventListener('click', function() {
        switchPeriod('monthly');
    });
});

function switchPeriod(period) {
    currentPeriod = period;

    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById(period + '-btn').classList.add('active');

    // Reload statistics
    loadStatistics();
}

async function loadStatistics() {
    try {
        const response = await fetch(`/api/stats?period=${currentPeriod}`);
        const stats = await response.json();

        displayStatistics(stats);
        updateCharts(stats);

    } catch (error) {
        console.error('Error loading statistics:', error);
        document.getElementById('stats-container').innerHTML = `
            <div class="col-12 text-center">
                <div class="alert alert-danger">
                    Failed to load statistics. Please try again later.
                </div>
            </div>
        `;
    }
}

function displayStatistics(stats) {
    const container = document.getElementById('stats-container');

    container.innerHTML = `
        <!-- Reports Statistics -->
        <div class="col-md-3">
            <div class="stats-card">
                <div class="metric-icon">📋</div>
                <div class="stats-number">${stats.total_reports}</div>
                <div class="stats-label">Total Reports</div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="stats-card">
                <div class="metric-icon">👥</div>
                <div class="stats-number">${stats.unique_users}</div>
                <div class="stats-label">Unique Users</div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="stats-card">
                <div class="metric-icon">🏘️</div>
                <div class="stats-number">${stats.community_groups}</div>
                <div class="stats-label">Community Groups</div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="stats-card">
                <div class="metric-icon">📱</div>
                <div class="stats-number">${stats.messages_sent}</div>
                <div class="stats-label">Messages Sent</div>
            </div>
        </div>

        <!-- Category Breakdown -->
        <div class="col-md-4">
            <div class="stats-card">
                <div class="metric-icon">🚨</div>
                <div class="stats-number">${stats.crime_reports}</div>
                <div class="stats-label">Crime Reports</div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="stats-card">
                <div class="metric-icon">🚑</div>
                <div class="stats-number">${stats.ems_reports}</div>
                <div class="stats-label">EMS Reports</div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="stats-card">
                <div class="metric-icon">🏗️</div>
                <div class="stats-number">${stats.infrastructure_reports}</div>
                <div class="stats-label">Infrastructure Reports</div>
            </div>
        </div>
    `;

    // Update financial metrics
    document.getElementById('total-cost').textContent = `R ${stats.total_cost.toFixed(2)}`;
    document.getElementById('messages-sent').textContent = stats.messages_sent;
    document.getElementById('cost-per-message').textContent = `R ${stats.cost_per_message.toFixed(2)}`;

    // Update additional metrics
    document.getElementById('active-areas').textContent = stats.active_areas;
    document.getElementById('avg-response-time').textContent = `${stats.avg_response_time} min`;
    document.getElementById('resolution-rate').textContent = `${stats.resolution_rate}%`;
    document.getElementById('peak-hours').textContent = stats.peak_hours;
}

function initializeCharts() {
    // Category Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    categoryChart = new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: ['Crime', 'EMS', 'Infrastructure'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: ['#ff6b6b', '#4ecdc4', '#45b7d1'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Time Chart
    const timeCtx = document.getElementById('timeChart').getContext('2d');
    timeChart = new Chart(timeCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Reports',
                data: [],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateCharts(stats) {
    // Update category chart
    categoryChart.data.datasets[0].data = [
        stats.crime_reports,
        stats.ems_reports,
        stats.infrastructure_reports
    ];
    categoryChart.update();

    // Update time chart
    timeChart.data.labels = stats.time_labels;
    timeChart.data.datasets[0].data = stats.time_data;
    timeChart.update();
}

// Donation functionality
function openDonationModal() {
    const modal = new bootstrap.Modal(document.getElementById('donationModal'));
    modal.show();
}

function selectAmount(amount) {
    document.getElementById('custom-amount').value = amount;

    // Update button states
    document.querySelectorAll('.donation-options .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
    });
    event.target.classList.remove('btn-outline-primary');
    event.target.classList.add('btn-primary');
}

async function donateViaYoco() {
    const amount = document.getElementById('custom-amount').value;
    if (!amount || parseFloat(amount) < 20) { // Yoco minimum is R2.00, correcting from 20
        alert('Please select or enter a donation amount of at least R20.00');
        return;
    }

    try {
        const response = await fetch('/api/payments/donate/yoco/create-checkout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ amount: parseFloat(amount) })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `Yoco API Error: ${response.status}`);
        }

        const checkoutData = await response.json();
        if (checkoutData.redirectUrl) {
            window.location.href = checkoutData.redirectUrl;
        } else {
            alert('Could not initiate Yoco payment. Missing redirect URL.');
        }
    } catch (error) {
        console.error('Yoco donation error:', error);
        alert(`Error initiating Yoco payment: ${error.message}. Please try again.`);
    }
}
/*
function donateViaEFT() {
    const amount = document.getElementById('custom-amount').value;
    if (!amount || amount < 10) {
        alert('Please select or enter a donation amount of at least R10');
        return;
    }

    // Show EFT details
    alert(`EFT Details:\nBank: FNB\nAccount Type: Current Account\NAccount: J Theron ~ Development Team\nAccount Number: ***********\nBranch: 201510\nReference: DONATION-${Date.now()}\nAmount: R${amount}`);
}
*/
</script>
{% endblock %}
