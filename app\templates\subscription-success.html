{% extends "base.html" %}

{% block title %}Subscription Successful - Hello Cyril API{% endblock %}

{% block extra_css %}
    <style>
        .success-container {
            background: linear-gradient(135deg, #ee4d4d 0%, #f27c7c 100%);
            color: white;
            padding: 100px 0;
            min-height: 100vh;
        }
        
        .success-card {
            background: white;
            color: #333;
            border-radius: 5px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            margin: 20px 0;
            font-family: "Dosis", arial, tahoma, verdana;
        }
        
        .api-key-display {
            background: #2b2e48;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            word-break: break-all;
            border: 2px solid #f27c7c;
            margin: 20px 0;
        }
        
        .copy-button {
            background: #ee4d4d;
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: "<PERSON><PERSON>", arial, tahoma, verdana;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .copy-button:hover {
            background: #d43f3f;
        }
        
        .success-icon {
            color: #ee4d4d;
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .info-box {
            background: #f8f9fa;
            border-left: 4px solid #ee4d4d;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .btn-hello-cyril {
            background: transparent;
            border: 2px solid #f27c7c;
            color: #f27c7c;
            border-radius: 5px;
            text-transform: uppercase;
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 12px;
            box-shadow: 2px 2px 0 #f27c7c;
            transition: all 0.3s ease;
            position: relative;
            padding: 10px 20px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-hello-cyril:hover {
            background: transparent;
            border-color: #f27c7c;
            color: #f27c7c;
            box-shadow: none;
            top: 2px;
            left: 2px;
            text-decoration: none;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="success-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="success-card text-center">
                        <i class="fas fa-check-circle success-icon"></i>
                        <h1>🎉 Subscription Successful!</h1>
                        <p class="lead">Welcome to the Hello Cyril API! Your subscription has been activated.</p>
                        
                        <div class="info-box text-left">
                            <h5><i class="fas fa-info-circle"></i> Your API Key</h5>
                            <p>Here's your API key. <strong>Please save it securely</strong> - you'll need it to access the API.</p>
                            
                            <div class="api-key-display" id="apiKeyDisplay">
                                <span id="apiKeyText">Loading...</span>
                            </div>
                            
                            <button class="copy-button" onclick="copyApiKey()">
                                <i class="fas fa-copy"></i> Copy API Key
                            </button>
                        </div>
                        
                        <div class="info-box text-left">
                            <h5><i class="fas fa-rocket"></i> Getting Started</h5>
                            <p>Use your API key in the header of your requests:</p>
                            <div class="api-key-display">
curl -H "X-API-Key: YOUR_API_KEY" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;"https://hellocyril.co.za/api/subscription/reports"
                            </div>
                        </div>
                        
                        <div class="info-box text-left">
                            <h5><i class="fas fa-book"></i> API Documentation</h5>
                            <ul>
                                <li><strong>Reports API:</strong> <code>/api/subscription/reports</code></li>
                                <li><strong>Statistics:</strong> <code>/api/subscription/stats</code></li>
                                <li><strong>Usage Info:</strong> <code>/api/subscription/usage</code></li>
                            </ul>
                        </div>
                        
                        <div class="info-box text-left">
                            <h5><i class="fas fa-key"></i> Lost Your API Key?</h5>
                            <p>You can retrieve your API key anytime using your email and contact name.</p>
                            <a href="/api-key-retrieval" class="btn-hello-cyril">Retrieve API Key</a>
                        </div>
                        
                        <div class="mt-4">
                            <a href="/" class="btn-hello-cyril me-3">Back to Home</a>
                            <a href="/subscription" class="btn-hello-cyril">View Plans</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        // Get API key from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const apiKey = urlParams.get('api_key');
        const subscriberId = urlParams.get('subscriber_id');
        
        if (apiKey) {
            document.getElementById('apiKeyText').textContent = apiKey;
        } else {
            document.getElementById('apiKeyText').textContent = 'API key not found in URL. Please contact support.';
        }
        
        function copyApiKey() {
            const apiKeyText = document.getElementById('apiKeyText').textContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(apiKeyText).then(function() {
                    // Change button text temporarily
                    const button = document.querySelector('.copy-button');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    button.style.background = '#28a745';
                    
                    setTimeout(function() {
                        button.innerHTML = originalText;
                        button.style.background = '#ee4d4d';
                    }, 2000);
                }).catch(function(err) {
                    console.error('Could not copy text: ', err);
                    fallbackCopyTextToClipboard(apiKeyText);
                });
            } else {
                fallbackCopyTextToClipboard(apiKeyText);
            }
        }
        
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            
            // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    const button = document.querySelector('.copy-button');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    button.style.background = '#28a745';
                    
                    setTimeout(function() {
                        button.innerHTML = originalText;
                        button.style.background = '#ee4d4d';
                    }, 2000);
                }
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
                alert('Please manually copy the API key');
            }
            
            document.body.removeChild(textArea);
        }
        
        // Auto-select API key text when clicked
        document.getElementById('apiKeyDisplay').addEventListener('click', function() {
            const range = document.createRange();
            range.selectNode(document.getElementById('apiKeyText'));
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
        });
    </script>
{% endblock %}
