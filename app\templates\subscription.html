{% extends "base.html" %}

{% block title %}Map View{% endblock %}

{% block extra_css %}

    <style>
        /* Match Hello Cyril red theme */
        .hero-section {
            background: linear-gradient(135deg, #ee4d4d 0%, #f27c7c 100%);
            color: white;
            padding: 100px 0;
        }

        .pricing-card {
            border: none;
            border-radius: 5px;
            box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            font-family: "Dosis", arial, tahoma, verdana;
        }

        .pricing-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 0 rgba(0, 0, 0, 0.15);
        }

        .pricing-card.featured {
            border: 3px solid #ee4d4d;
            transform: scale(1.02);
        }

        .pricing-card.featured .card-header {
            background: #ee4d4d;
            color: white;
        }

        .pricing-card .card-header {
            background-color: #ee4d4d;
            color: white;
            border-radius: 3px 3px 0 0 !important;
            font-family: "Dosis", arial, tahoma, verdana;
            font-weight: 300;
        }

        .price {
            font-size: 3rem;
            font-weight: bold;
            color: #ee4d4d;
            font-family: "Dosis", arial, tahoma, verdana;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            font-family: "Dosis", arial, tahoma, verdana;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list i {
            color: #ee4d4d;
            margin-right: 10px;
        }

        .api-demo {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #f27c7c;
        }

        .code-block {
            background: #2b2e48;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            border: 2px solid #f27c7c;
        }

        /* Hello Cyril button styling */
        .btn-hello-cyril {
            background: transparent;
            border: 2px solid #f27c7c;
            color: #f27c7c;
            border-radius: 5px;
            text-transform: uppercase;
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 12px;
            box-shadow: 2px 2px 0 #f27c7c;
            transition: all 0.3s ease;
            position: relative;
        }

        .btn-hello-cyril:hover {
            background: transparent;
            border-color: #f27c7c;
            color: #f27c7c;
            box-shadow: none;
            top: 2px;
            left: 2px;
        }

        .btn-hello-cyril.btn-primary {
            background: #ee4d4d;
            border-color: #ee4d4d;
            color: white;
            box-shadow: 2px 2px 0 #f27c7c;
        }

        .btn-hello-cyril.btn-primary:hover {
            background: #ee4d4d;
            border-color: #ee4d4d;
            color: white;
            box-shadow: none;
            top: 2px;
            left: 2px;
        }

        /* Feature icons */
        .feature-icon {
            background: #ee4d4d;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        /* Navigation styling to match */
        .navbar-brand {
            color: #ee4d4d !important;
            font-family: "Dosis", arial, tahoma, verdana;
            font-weight: 500;
        }

        .navbar-brand span {
            color: #f7aaaa;
            font-weight: 300;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: "Dosis", arial, tahoma, verdana;
            font-weight: 500;
        }

        p, li {
            font-family: "Dosis", arial, tahoma, verdana;
            font-size: 1.1em;
        }

        /* Footer styling */
        .footer-hello-cyril {
            background: #2b2e48;
            color: white;
        }

        .footer-hello-cyril a {
            color: #f27c7c;
            text-decoration: none;
        }

        .footer-hello-cyril a:hover {
            color: #ee4d4d;
        }
    </style>
{% endblock %}

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-4">Hello Cyril API</h1>
            <p class="lead mb-5">Access real-time crime, EMS, and infrastructure data through our powerful API</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="api-demo">
                        <h5 class="text-dark mb-3">Try our API:</h5>
                        <div class="code-block">
curl -H "X-API-Key: your_api_key" \<br>
&nbsp;&nbsp;&nbsp;&nbsp;"https://hellocyril.co.za/api/subscription/reports?category=crime&limit=10"
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 mb-3">Choose Your Plan</h2>
                <p class="lead text-muted">Flexible pricing for developers, businesses, and enterprises</p>
            </div>

            <div class="row g-4">
                <!-- Basic Plan -->
                <div class="col-lg-4">
                    <div class="card pricing-card">
                        <div class="card-header bg-light text-center py-4">
                            <h4 class="card-title">Basic</h4>
                            <p class="text-muted">Perfect for testing and small projects</p>
                        </div>
                        <div class="card-body text-center">
                            <div class="price mb-4">R99<small class="text-muted fs-6">/month</small></div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> 10,000 API requests/month</li>
                                <li><i class="fas fa-check"></i> Access to reports API</li>
                                <li><i class="fas fa-check"></i> Basic statistics</li>
                                <li><i class="fas fa-check"></i> Email support</li>
                                <li><i class="fas fa-check"></i> 100 requests/minute</li>
                                <li><i class="fas fa-times text-muted"></i> Real-time alerts</li>
                                <li><i class="fas fa-times text-muted"></i> Webhooks</li>
                            </ul>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-hello-cyril w-100" onclick="selectPlan('basic')">
                                Get Started
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Premium Plan -->
                <div class="col-lg-4">
                    <div class="card pricing-card featured">
                        <div class="card-header text-center py-4">
                            <h4 class="card-title">Premium</h4>
                            <p class="text-light">Most popular for growing businesses</p>
                            <span class="badge bg-warning text-dark">POPULAR</span>
                        </div>
                        <div class="card-body text-center">
                            <div class="price mb-4">R299<small class="text-muted fs-6">/month</small></div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> 50,000 API requests/month</li>
                                <li><i class="fas fa-check"></i> Full reports API access</li>
                                <li><i class="fas fa-check"></i> Advanced statistics</li>
                                <li><i class="fas fa-check"></i> Community groups API</li>
                                <li><i class="fas fa-check"></i> Real-time alerts</li>
                                <li><i class="fas fa-check"></i> Priority email support</li>
                                <li><i class="fas fa-check"></i> 500 requests/minute</li>
                            </ul>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-hello-cyril btn-primary w-100" onclick="selectPlan('premium')">
                                Start Premium
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Enterprise Plan -->
                <div class="col-lg-4">
                    <div class="card pricing-card">
                        <div class="card-header bg-dark text-white text-center py-4">
                            <h4 class="card-title">Enterprise</h4>
                            <p class="text-light">For large-scale applications</p>
                        </div>
                        <div class="card-body text-center">
                            <div class="price mb-4">R999<small class="text-muted fs-6">/month</small></div>
                            <ul class="feature-list">
                                <li><i class="fas fa-check"></i> 200,000 API requests/month</li>
                                <li><i class="fas fa-check"></i> Full API access</li>
                                <li><i class="fas fa-check"></i> Real-time webhooks</li>
                                <li><i class="fas fa-check"></i> Custom geographic filters</li>
                                <li><i class="fas fa-check"></i> Priority support & SLA</li>
                                <li><i class="fas fa-check"></i> Custom integrations</li>
                                <li><i class="fas fa-check"></i> 2,000 requests/minute</li>
                            </ul>
                        </div>
                        <div class="card-footer bg-transparent">
                            <button class="btn btn-hello-cyril w-100" onclick="selectPlan('enterprise')" style="background: #2b2e48; border-color: #2b2e48; color: white;">
                                Contact Sales
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-6 mb-3">API Features</h2>
                <p class="lead text-muted">Everything you need to build amazing applications</p>
            </div>

            <div class="row g-4">
                <div class="col-md-6 col-lg-3">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-database fa-lg"></i>
                        </div>
                        <h5>Real-time Data</h5>
                        <p class="text-muted">Access live crime, EMS, and infrastructure reports as they happen</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-map-marker-alt fa-lg"></i>
                        </div>
                        <h5>Geographic Filtering</h5>
                        <p class="text-muted">Filter data by location, radius, or custom geographic boundaries</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-bell fa-lg"></i>
                        </div>
                        <h5>Instant Alerts</h5>
                        <p class="text-muted">Receive webhooks for new reports matching your criteria</p>
                    </div>
                </div>
                <div class="col-md-6 col-lg-3">
                    <div class="text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar fa-lg"></i>
                        </div>
                        <h5>Analytics</h5>
                        <p class="text-muted">Comprehensive statistics and trend analysis</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Subscription Form Modal -->
    <div class="modal fade" id="subscriptionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Subscribe to Hello Cyril API</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="subscriptionForm">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="contactName" class="form-label">Contact Name</label>
                            <input type="text" class="form-control" id="contactName" required>
                        </div>
                        <div class="mb-3">
                            <label for="companyName" class="form-label">Company Name (Optional)</label>
                            <input type="text" class="form-control" id="companyName">
                        </div>
                        <div class="mb-3">
                            <label for="phoneNumber" class="form-label">Phone Number (Optional)</label>
                            <input type="tel" class="form-control" id="phoneNumber" placeholder="+27xxxxxxxxx">
                        </div>
                        <div class="mb-3">
                            <label for="selectedPlan" class="form-label">Selected Plan</label>
                            <input type="text" class="form-control" id="selectedPlan" readonly>
                        </div>
                        <div id="alertContainer"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-hello-cyril" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-hello-cyril btn-primary" onclick="submitSubscription()">Subscribe</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer 
    <footer class="footer-hello-cyril py-4">
        <div class="container text-center">
            <p>&copy; 2024 Hello Cyril. All rights reserved.</p>
            <p>
                <a href="/feedback">Contact Us</a> |
                <a href="/stats">Statistics</a> |
                <a href="/map">Live Map</a> |
                <a href="/">Home</a>
            </p>
        </div>
    </footer>
    -->
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let selectedPlanType = '';

        function selectPlan(planType) {
            selectedPlanType = planType;

            const planNames = {
                'basic': 'Basic Plan - R99/month',
                'premium': 'Premium Plan - R299/month',
                'enterprise': 'Enterprise Plan - R999/month'
            };

            document.getElementById('selectedPlan').value = planNames[planType];

            // Show subscription modal
            const modal = new bootstrap.Modal(document.getElementById('subscriptionModal'));
            modal.show();
        }

        async function submitSubscription() {
            const form = document.getElementById('subscriptionForm');
            const alertContainer = document.getElementById('alertContainer');

            // Get form data
            const formData = {
                email: document.getElementById('email').value,
                contact_name: document.getElementById('contactName').value,
                company_name: document.getElementById('companyName').value,
                phone_number: document.getElementById('phoneNumber').value,
                subscription_tier: selectedPlanType
            };

            // Validate required fields
            if (!formData.email || !formData.contact_name) {
                alertContainer.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        Please fill in all required fields.
                    </div>
                `;
                return;
            }

            try {
                // Submit subscription
                const response = await fetch('/api/subscription/subscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok) {
                    alertContainer.innerHTML = `
                        <div class="alert alert-success" role="alert">
                            <strong>Success!</strong> Your subscription has been created.
                            We'll contact you shortly to complete the setup and payment.
                        </div>
                    `;

                    // Clear form
                    form.reset();

                    // Close modal after delay
                    setTimeout(() => {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('subscriptionModal'));
                        modal.hide();
                    }, 3000);

                } else {
                    alertContainer.innerHTML = `
                        <div class="alert alert-danger" role="alert">
                            <strong>Error:</strong> ${result.detail || 'Failed to create subscription'}
                        </div>
                    `;
                }

            } catch (error) {
                alertContainer.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <strong>Error:</strong> Failed to submit subscription. Please try again.
                    </div>
                `;
            }
        }
    </script>
