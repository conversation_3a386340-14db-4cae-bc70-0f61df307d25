{% extends "base.html" %}

{% block title %}Incident Map{% endblock %}

{% block extra_css %}
<!-- Include Flatpickr for better date picking -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<style>
  .report-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .report-controls {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
  }

  .control-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;
  }

  .control-group {
    flex: 1;
    min-width: 200px;
  }

  .control-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .control-group select,
  .control-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .map-selection-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
  }

  #map {
    height: 400px;
    width: 100%;
    border-radius: 5px;
  }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  .generate-btn {
    background-color: #ee4d4d;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
  }

  .generate-btn:hover {
    background-color: #d32f2f;
  }

  .reset-btn {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #333;
    cursor: pointer;
    font-family: "Dosis", sans-serif;
    font-size: 16px;
    padding: 10px 20px;
    text-transform: uppercase;
    transition: background-color 0.3s ease;
  }

  .reset-btn:hover {
    background-color: #e0e0e0;
  }

  .report-card {
    margin-bottom: 15px;
    background: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 3px 0 rgba(0, 0, 0, 0.1);
  }

  .legend-item {
    margin-bottom: 5px;
  }

  .legend-color {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 5px;
    border-radius: 50%;
    vertical-align: middle;
  }

  .category-badge {
    border-radius: 20px;
    display: inline-block;
    font-size: 0.8em;
    margin-left: 10px;
    padding: 3px 10px;
    text-transform: uppercase;
  }

  .category-crime {
    background-color: #ffebee;
    color: #c62828;
  }

  .category-ems {
    background-color: #e3f2fd;
    color: #1565c0;
  }

  .category-infrastructure {
    background-color: #fff8e1;
    color: #ff8f00;
  }

  .incident-meta {
    color: #888;
    font-size: 0.9em;
    margin-bottom: 10px;
  }

  /* Flatpickr customization */
  .flatpickr-calendar {
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
  }

  .flatpickr-day.selected,
  .flatpickr-day.startRange,
  .flatpickr-day.endRange,
  .flatpickr-day.selected.inRange,
  .flatpickr-day.startRange.inRange,
  .flatpickr-day.endRange.inRange,
  .flatpickr-day.selected:focus,
  .flatpickr-day.startRange:focus,
  .flatpickr-day.endRange:focus,
  .flatpickr-day.selected:hover,
  .flatpickr-day.startRange:hover,
  .flatpickr-day.endRange:hover,
  .flatpickr-day.selected.prevMonthDay,
  .flatpickr-day.startRange.prevMonthDay,
  .flatpickr-day.endRange.prevMonthDay,
  .flatpickr-day.selected.nextMonthDay,
  .flatpickr-day.startRange.nextMonthDay,
  .flatpickr-day.endRange.nextMonthDay {
    background: #ee4d4d;
    border-color: #ee4d4d;
  }

  .flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
  .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
  .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
    box-shadow: -10px 0 0 #ee4d4d;
  }

  .flatpickr-months .flatpickr-month {
    background: #ee4d4d;
  }

  .flatpickr-months .flatpickr-prev-month,
  .flatpickr-months .flatpickr-next-month {
    color: rgba(255, 255, 255, 0.9);
    fill: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month .flatpickr-monthDropdown-months {
    background: #ee4d4d;
    color: white;
  }

  .flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
    background-color: #ee4d4d;
  }

  .flatpickr-current-month .numInputWrapper span.arrowUp:after {
    border-bottom-color: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month .numInputWrapper span.arrowDown:after {
    border-top-color: rgba(255, 255, 255, 0.9);
  }

  .flatpickr-current-month input.cur-year {
    color: white;
  }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">INCIDENT MAP</h1>

<div class="report-container">
  <div class="report-controls">
    <h2>Map Filters</h2>
    <div class="control-row">
      <div class="control-group">
        <label for="category-filter">Category:</label>
        <select id="category-filter">
          <option value="all">All Categories</option>
          <option value="crime">Crime</option>
          <option value="ems">EMS</option>
          <option value="infrastructure">Infrastructure</option>
        </select>
      </div>
      <div class="control-group">
        <label for="start-date">Start Date:</label>
        <input type="text" id="start-date" placeholder="Select start date" readonly>
      </div>
      <div class="control-group">
        <label for="end-date">End Date:</label>
        <input type="text" id="end-date" placeholder="Select end date" readonly>
      </div>
      <div class="control-group">
        <label for="verified-filter">Status:</label>
        <select id="verified-filter">
          <option value="all">All Reports</option>
          <option value="verified">Verified Only</option>
          <option value="unverified">Unverified Only</option>
        </select>
      </div>
    </div>
  </div>

  <div class="map-selection-container">
    <div id="map"></div>
    <div class="action-buttons">
      <button class="reset-btn" id="reset-filters">Reset Filters</button>
      <button class="generate-btn" id="apply-filters">Apply Filters</button>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="card mb-4">
        <div class="card-header">
          <h2 class="h5 mb-0">Legend</h2>
        </div>
        <div class="card-body">
          <div class="legend-item">
            <span class="legend-color" style="background-color: #dc3545;"></span>
            <span>Crime</span>
          </div>
          <div class="legend-item">
            <span class="legend-color" style="background-color: #28a745;"></span>
            <span>EMS</span>
          </div>
          <div class="legend-item">
            <span class="legend-color" style="background-color: #ffc107;"></span>
            <span>Infrastructure</span>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-8">
      <div class="card">
        <div class="card-header">
          <h2 class="h5 mb-0">Recent Reports</h2>
        </div>
        <div class="card-body">
          <div id="reports-list">
            <!-- Reports will be loaded here -->
            <div class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div id="alerts"></div>
{% endblock %}

{% block extra_js %}
<script>
  // Initialize variables
  let map;
  let markers = [];
  let reports = [];

  // Category colors
  const categoryColors = {
    'crime': '#dc3545',
    'ems': '#28a745',
    'infrastructure': '#ffc107'
  };

  // Initialize maps and controls when the page loads
  document.addEventListener('DOMContentLoaded', function() {
    initMap();
    initDatepickers();
    setupEventListeners();
  });

  // Initialize the map
  function initMap() {
    // Create the map centered on South Africa
    map = L.map('map').setView([-30.5595, 22.9375], 6);

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Load initial reports
    loadReports();
  }

  // Initialize datepickers using Flatpickr
  function initDatepickers() {
    // Set default dates (last 7 days)
    const today = new Date();
    const weekAgo = new Date();
    weekAgo.setDate(today.getDate() - 7);

    // Format dates for display
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    // Initialize start date picker with Flatpickr
    flatpickr("#start-date", {
      dateFormat: "Y-m-d",
      defaultDate: weekAgo,
      maxDate: today,
      disableMobile: "true",
      onChange: function(selectedDates, dateStr) {
        // Update end date picker's min date
        endDatePicker.set('minDate', dateStr);
      }
    });

    // Initialize end date picker with Flatpickr
    const endDatePicker = flatpickr("#end-date", {
      dateFormat: "Y-m-d",
      defaultDate: today,
      maxDate: today,
      minDate: formatDate(weekAgo),
      disableMobile: "true"
    });
  }

  // Set up event listeners
  function setupEventListeners() {
    // Apply filters button
    document.getElementById('apply-filters').addEventListener('click', loadReports);

    // Reset filters button
    document.getElementById('reset-filters').addEventListener('click', resetFilters);
  }

  // Load reports from the API
  async function loadReports() {
    try {
      // Get filter values
      const category = document.getElementById('category-filter').value;
      const startDate = document.getElementById('start-date').value;
      const endDate = document.getElementById('end-date').value;
      const verified = document.getElementById('verified-filter').value;

      // Calculate days from start date to today
      const start = new Date(startDate);
      const end = new Date(endDate);
      const today = new Date();
      const daysDiff = Math.ceil((today - start) / (1000 * 60 * 60 * 24));

      // Show loading indicator
      document.getElementById('reports-list').innerHTML = `
        <div class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      `;

      // Fetch reports from API
      const response = await fetch(`/api/reports/?days=${daysDiff}`);
      const data = await response.json();

      // Filter reports by category and date range
      reports = filterReportsByCategory(data, category, start, end, verified);

      // Clear existing markers
      clearMarkers();

      // Add markers for each report
      reports.forEach(report => addMarker(report));

      // Update the reports list
      updateReportsList();

      // Show success message
      showAlert('Reports loaded successfully. Found ' + reports.length + ' reports.', 'success');
    } catch (error) {
      console.error('Error loading reports:', error);
      showAlert('Error loading reports. Please try again.', 'danger');
    }
  }

  // Filter reports by category and date range
  function filterReportsByCategory(reports, category, startDate, endDate, verified) {
    return reports.filter(report => {
      // Check if report matches the selected category
      const matchesCategory = category === 'all' || report.category === category;

      // Check if report is within the date range
      const reportDate = new Date(report.timestamp);
      const isInDateRange = reportDate >= startDate && reportDate <= endDate;

      // Check if report matches the verification status
      let matchesVerification = true;
      if (verified === 'verified') {
        matchesVerification = report.verified === true;
      } else if (verified === 'unverified') {
        matchesVerification = report.verified === false;
      }

      return matchesCategory && isInDateRange && matchesVerification;
    });
  }

  // Add a marker to the map
  function addMarker(report) {
    // Get the color based on the category
    const color = categoryColors[report.category] || '#3388ff';

    // Create a marker with a custom icon
    const marker = L.circleMarker([report.latitude, report.longitude], {
      radius: 8,
      fillColor: color,
      color: '#fff',
      weight: 1,
      opacity: 1,
      fillOpacity: 0.8
    }).addTo(map);

    // Add a popup with report details
    marker.bindPopup(`
      <div class="report-popup">
        <h3>${formatCategory(report.category)}</h3>
        <p><strong>ID:</strong> ${report.short_id || report.id.substring(0, 8)}</p>
        <p><strong>Time:</strong> ${formatDate(report.timestamp)}</p>
        <p><strong>Status:</strong> ${report.verified ? 'Verified' : 'Under Review'}</p>
        <p>${report.description}</p>
        ${report.image_url ? `<img src="${report.image_url}" alt="Report Image" class="img-fluid">` : ''}
      </div>
    `);

    // Store the marker
    markers.push(marker);
  }

  // Clear all markers from the map
  function clearMarkers() {
    markers.forEach(marker => map.removeLayer(marker));
    markers = [];
  }

  // Update the reports list
  function updateReportsList() {
    const reportsList = document.getElementById('reports-list');

    // Clear the list
    reportsList.innerHTML = '';

    // Add each report to the list
    if (reports.length === 0) {
      reportsList.innerHTML = '<div class="alert alert-info">No reports found matching the filters.</div>';
      return;
    }

    reports.slice(0, 10).forEach(report => {
      const categoryClass = `category-${report.category}`;
      const formattedDate = new Date(report.timestamp).toLocaleString();

      const card = document.createElement('div');
      card.className = 'report-card';
      card.innerHTML = `
        <h4>
          Incident #${report.id.substring(0, 8)}
          <span class="category-badge ${categoryClass}">${report.category}</span>
        </h4>
        <div class="incident-meta">
          Reported: ${formattedDate}
        </div>
        <p>${report.description.substring(0, 100)}${report.description.length > 100 ? '...' : ''}</p>
        <button class="btn view-on-map" data-lat="${report.latitude}" data-lng="${report.longitude}">View on Map</button>
      `;

      reportsList.appendChild(card);

      // Add event listener to the "View on Map" button
      card.querySelector('.view-on-map').addEventListener('click', function() {
        const lat = parseFloat(this.getAttribute('data-lat'));
        const lng = parseFloat(this.getAttribute('data-lng'));
        map.setView([lat, lng], 16);

        // Find the marker and open its popup
        markers.forEach(marker => {
          const markerLatLng = marker.getLatLng();
          if (markerLatLng.lat === lat && markerLatLng.lng === lng) {
            marker.openPopup();
          }
        });
      });
    });
  }

  // Reset filters
  function resetFilters() {
    document.getElementById('category-filter').value = 'all';
    document.getElementById('verified-filter').value = 'all';

    // Reset date pickers
    const today = new Date();
    const weekAgo = new Date();
    weekAgo.setDate(today.getDate() - 7);

    // Get the flatpickr instances
    const startDatePicker = document.getElementById('start-date')._flatpickr;
    const endDatePicker = document.getElementById('end-date')._flatpickr;

    if (startDatePicker) {
      startDatePicker.setDate(weekAgo);
    }

    if (endDatePicker) {
      endDatePicker.setDate(today);
    }

    // Reload reports
    loadReports();
  }

  // Format a category
  function formatCategory(category) {
    if (!category) return 'Unknown';
    return category.charAt(0).toUpperCase() + category.slice(1);
  }

  // Format a date
  function formatDate(dateString) {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  // Show an alert message
  function showAlert(message, type) {
    const alertsContainer = document.getElementById('alerts');
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    alertsContainer.appendChild(alert);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
      alert.classList.remove('show');
      setTimeout(() => alert.remove(), 300);
    }, 5000);
  }
</script>
{% endblock %}
