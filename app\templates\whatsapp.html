{% extends "base.html" %}

{% block title %}WhatsApp Chat{% endblock %}

{% block extra_css %}
<style>
    .test-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        font-weight: bold;
        margin-bottom: 5px;
        font-family: "Dosis", arial, tahoma, verdana;
    }
    
    .form-control {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: "Dosis", arial, tahoma, verdana;
    }
    
    .btn-primary {
        background-color: #ee4d4d;
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-family: "Dosis", sans-serif;
        text-transform: uppercase;
    }
    
    .chat-container {
        display: flex;
        flex-direction: column;
        height: 400px;
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: hidden;
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 10px;
        background-color: #f5f5f5;
    }
    
    .message {
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 5px;
        max-width: 80%;
    }
    
    .user-message {
        background-color: #dcf8c6;
        align-self: flex-end;
        margin-left: auto;
    }
    
    .bot-message {
        background-color: white;
        align-self: flex-start;
    }
    
    .chat-input {
        display: flex;
        padding: 10px;
        background-color: white;
        border-top: 1px solid #ddd;
    }
    
    .chat-input input {
        flex: 1;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 20px;
        margin-right: 10px;
    }
    
    .chat-input button {
        background-color: #ee4d4d;
        border: none;
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">WHATSAPP CHAT</h1>

<div class="row">
    <div class="col-md-6">
        <div class="test-container">
            <h2>Test Conversation</h2>
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="message bot-message">
                        Welcome to Hello Cyril WhatsApp Bot Test. Type a message to begin.
                    </div>
                </div>
                <div class="chat-input">
                    <input type="text" id="message-input" placeholder="Type a message...">
                    <button id="send-button">Send</button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">

    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const chatMessages = document.getElementById('chat-messages');

        let userLocation = { latitude: null, longitude: null };

        // Get user's location
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                userLocation.latitude = position.coords.latitude;
                userLocation.longitude = position.coords.longitude;
                console.log('Location retrieved:', userLocation);
            }, function(error) {
                console.error('Error retrieving location:', error);
            });
        }

        sendButton.addEventListener('click', sendMessage);

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            addMessage(message, 'user');
            messageInput.value = '';

            const requestData = {
                message: message,
                latitude: userLocation.latitude,
                longitude: userLocation.longitude
            };

            // Send the message as a report to the FastAPI backend
            fetch('/api/v1/log-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.response) {
                    addMessage(data.response, 'bot');
                } else {
                    addMessage('Report logged successfully!', 'bot');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addMessage('Error: Could not log the report.', 'bot');
            });
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    });
</script>
{% endblock %}
