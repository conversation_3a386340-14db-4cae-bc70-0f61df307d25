{% extends "base.html" %}

{% block title %}WhatsApp Integration Test{% endblock %}

{% block extra_css %}
<style>
    .test-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        font-weight: bold;
        margin-bottom: 5px;
        font-family: "Dosis", arial, tahoma, verdana;
    }

    .form-control {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: "Dosis", arial, tahoma, verdana;
    }

    .btn-primary {
        background-color: #ee4d4d;
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-family: "Dosis", sans-serif;
        text-transform: uppercase;
    }

    .chat-container {
        display: flex;
        flex-direction: column;
        height: 400px;
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: hidden;
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 10px;
        background-color: #f5f5f5;
    }

    .message {
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 5px;
        max-width: 80%;
    }

    .user-message {
        background-color: #dcf8c6;
        align-self: flex-end;
        margin-left: auto;
    }

    .bot-message {
        background-color: white;
        align-self: flex-start;
    }

    .chat-input {
        display: flex;
        padding: 10px;
        background-color: white;
        border-top: 1px solid #ddd;
    }

    .chat-input input {
        flex: 1;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 20px;
        margin-right: 10px;
    }

    .chat-input button {
        background-color: #ee4d4d;
        border: none;
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<h1 class="project-name">WHATSAPP INTEGRATION TEST</h1>

<div class="row">
    <div class="col-md-6">
        <div class="test-container">
            <h2>Test Conversation</h2>
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="message bot-message">
                        Welcome to Hello Cyril WhatsApp Bot Test. Type a message to begin.
                    </div>
                </div>
                <div class="chat-input">
                    <input type="text" id="message-input" placeholder="Type a message...">
                    <button id="send-button">Send</button>
                </div>
            </div>
            <div class="form-group mt-3">
                <label for="from-number">From Number:</label>
                <input type="text" class="form-control" id="from-number" value="whatsapp:+1234567890">
            </div>
            <div class="form-group">
                <label for="latitude">Latitude:</label>
                <input type="text" class="form-control" id="latitude" placeholder="Optional">
            </div>
            <div class="form-group">
                <label for="longitude">Longitude:</label>
                <input type="text" class="form-control" id="longitude" placeholder="Optional">
            </div>
            <div class="form-group">
                <label for="media-url">Media URL:</label>
                <input type="text" class="form-control" id="media-url" placeholder="Optional">
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="test-container">

            <h2 class="mt-4">Conversation State</h2>
            <pre id="conversation-state" style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow: auto; max-height: 200px;">No conversation started</pre>

            <h2 class="mt-4">API Response</h2>
            <pre id="api-response" style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow: auto; max-height: 200px;">No response yet</pre>

            <h2>WhatsApp Configuration</h2>
            <button class="btn-primary" onclick="checkConfig()">Check Configuration</button>
            <div id="config-status" style="margin-top: 15px;"></div>

            <h2 class="mt-4">Send Direct WhatsApp Message</h2>
            <form id="whatsapp-form">
                <div class="form-group">
                    <label for="whatsapp-to">To (Admin Phone Number):</label>
                    <input type="text" class="form-control" id="whatsapp-to" placeholder="e.g., +27821234567">
                    <small style="display: block; margin-top: 5px;">
                        <strong>Examples:</strong><br>
                        • Your number: +27821234567<br>
                        • Test number: +15005550006<br>
                        • Admin contact: +27646819553<br><br>
                        <strong>Note:</strong> The system sends notifications to group admin phone numbers only.<br>
                        Admins can then share important alerts with their WhatsApp groups manually.
                    </small>
                </div>
                <div class="form-group">
                    <label for="whatsapp-message">Message:</label>
                    <textarea class="form-control" id="whatsapp-message" rows="4" placeholder="Enter your test message...">🚨 TEST MESSAGE from Hello Cyril

This is a test message to verify WhatsApp integration.

Time: [CURRENT_TIME]

If you receive this, the API is working!</textarea>
                </div>
                <button type="button" class="btn-primary" id="send-whatsapp">Send WhatsApp Message</button>
                <button type="button" class="btn-primary" id="check-group-link" style="background-color: #6c757d; margin-left: 10px;">Check Group Link</button>
            </form>
            <div id="whatsapp-result" style="margin-top: 15px;"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const chatMessages = document.getElementById('chat-messages');
        const fromNumber = document.getElementById('from-number');
        const latitude = document.getElementById('latitude');
        const longitude = document.getElementById('longitude');
        const mediaUrl = document.getElementById('media-url');
        const conversationState = document.getElementById('conversation-state');
        const apiResponse = document.getElementById('api-response');

        // Send message when button is clicked
        sendButton.addEventListener('click', sendMessage);

        // Send message when Enter key is pressed
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');

            // Clear input
            messageInput.value = '';

            // Prepare request data
            const requestData = {
                message: message,
                from_number: fromNumber.value,
                latitude: latitude.value ? parseFloat(latitude.value) : null,
                longitude: longitude.value ? parseFloat(longitude.value) : null,
                media_url: mediaUrl.value || null
            };

            // Send request to API
            fetch('/api/whatsapp/test-conversation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                // Display API response
                apiResponse.textContent = JSON.stringify(data, null, 2);

                // Display conversation state
                if (data.current_state) {
                    conversationState.textContent = JSON.stringify({
                        state: data.current_state,
                        from_number: data.from_number
                    }, null, 2);
                }

                // Add bot response to chat
                if (data.response) {
                    addMessage(data.response, 'bot');
                } else if (data.message_type === 'completed_report') {
                    addMessage('Thank you for your report! Your incident has been recorded.', 'bot');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addMessage('Error: Could not process your message.', 'bot');
            });
        }

        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // WhatsApp direct message functionality
        const sendWhatsAppButton = document.getElementById('send-whatsapp');
        const checkGroupLinkButton = document.getElementById('check-group-link');
        const whatsappTo = document.getElementById('whatsapp-to');
        const whatsappMessage = document.getElementById('whatsapp-message');
        const whatsappResult = document.getElementById('whatsapp-result');

        // Set current time in default message
        whatsappMessage.value = whatsappMessage.value.replace('[CURRENT_TIME]', new Date().toLocaleString());

        sendWhatsAppButton.addEventListener('click', function() {
            const to = whatsappTo.value.trim();
            const message = whatsappMessage.value.trim();

            if (!to || !message) {
                alert('Please provide both recipient and message.');
                return;
            }

            whatsappResult.innerHTML = '<div style="color: blue;">Sending message...</div>';

            fetch('/api/whatsapp/test-whatsapp-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    to: to,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    whatsappResult.innerHTML = `
                        <div style="color: green; background: #d4edda; padding: 10px; border-radius: 4px;">
                            ✅ SUCCESS: Message sent successfully!<br>
                            To: ${data.to}<br>
                            Time: ${data.timestamp}
                        </div>
                    `;
                } else {
                    whatsappResult.innerHTML = `
                        <div style="color: red; background: #f8d7da; padding: 10px; border-radius: 4px;">
                            ❌ ERROR: ${data.detail || 'Unknown error'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                whatsappResult.innerHTML = `
                    <div style="color: red; background: #f8d7da; padding: 10px; border-radius: 4px;">
                        ❌ Network Error: ${error.message}
                    </div>
                `;
            });
        });

        // Check group link functionality
        checkGroupLinkButton.addEventListener('click', function() {
            const to = whatsappTo.value.trim();

            if (!to) {
                alert('Please enter a WhatsApp group link or ID first');
                return;
            }

            whatsappResult.innerHTML = '<div style="color: blue;">Checking group link...</div>';

            fetch('/api/whatsapp/extract-group-id', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    group_invite_link: to
                })
            })
            .then(response => response.json())
            .then(data => {
                whatsappResult.innerHTML = `
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; color: #856404;">
                        <h4>❌ ${data.error}</h4>
                        <p><strong>Problem:</strong> ${data.explanation.problem}</p>
                        <p><strong>Solution:</strong> ${data.explanation.solution}</p>
                        <p><strong>How to get the correct group ID:</strong></p>
                        <ol>
                            ${data.explanation.how_to_get_group_id.map(step => `<li>${step}</li>`).join('')}
                        </ol>
                        <p><strong>Alternative for testing:</strong> ${data.explanation.alternative}</p>
                        <p><strong>Test numbers you can use:</strong></p>
                        <ul>
                            ${data.test_numbers.map(number => `<li>${number}</li>`).join('')}
                        </ul>
                    </div>
                `;
            })
            .catch(error => {
                console.error('Error:', error);
                whatsappResult.innerHTML = `
                    <div style="color: red; background: #f8d7da; padding: 10px; border-radius: 4px;">
                        ❌ Error: ${error.message}
                    </div>
                `;
            });
        });
    });

    // Check WhatsApp configuration
    async function checkConfig() {
        const statusDiv = document.getElementById('config-status');
        statusDiv.innerHTML = 'Checking configuration...';

        try {
            const response = await fetch('/api/whatsapp/whatsapp-config');
            const data = await response.json();

            let statusClass = data.configured ? 'success' : 'error';
            let statusText = data.configured ? '✅ WhatsApp API is configured' : '❌ WhatsApp API is not fully configured';

            statusDiv.innerHTML = `
                <div style="padding: 10px; border-radius: 4px; background: ${data.configured ? '#d4edda' : '#f8d7da'}; color: ${data.configured ? '#155724' : '#721c24'};">
                    <strong>${statusText}</strong><br>
                    ${data.message}<br><br>
                    <strong>Configuration Details:</strong><br>
                    • API URL: ${data.config_status.whatsapp_api_url ? '✅' : '❌'}<br>
                    • Account SID: ${data.config_status.twilio_account_sid ? '✅' : '❌'}<br>
                    • Auth Token: ${data.config_status.twilio_auth_token ? '✅' : '❌'}<br>
                    • From Number: ${data.config_status.twilio_from_number ? '✅' : '❌'}<br>
                    • WhatsApp Number: ${data.config_status.whatsapp_number}
                </div>
            `;
        } catch (error) {
            statusDiv.innerHTML = `
                <div style="padding: 10px; border-radius: 4px; background: #f8d7da; color: #721c24;">
                    <strong>❌ Error checking configuration</strong><br>
                    ${error.message}
                </div>
            `;
        }
    }


</script>
{% endblock %}
