#!/usr/bin/env python3
"""
Script to clear sample/dummy analytics data and start fresh with real tracking
"""

import sys
import os
from sqlalchemy import text

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.models.database import get_db

def clear_sample_data():
    """Clear all sample analytics data"""
    
    db = next(get_db())
    
    try:
        print("Clearing sample analytics data...")
        
        # Clear page visits
        result = db.execute(text("DELETE FROM page_visits"))
        print(f"✅ Cleared {result.rowcount} page visit records")
        
        # Clear WhatsApp clicks
        result = db.execute(text("DELETE FROM whatsapp_clicks"))
        print(f"✅ Cleared {result.rowcount} WhatsApp click records")
        
        # Clear donation tracking
        result = db.execute(text("DELETE FROM donation_tracking"))
        print(f"✅ Cleared {result.rowcount} donation tracking records")
        
        db.commit()
        print("\n🎉 Successfully cleared all sample analytics data!")
        print("\nThe analytics system is now ready to track real user data.")
        print("\nTo start tracking:")
        print("1. Visit your homepage to generate page visit data")
        print("2. Click WhatsApp links to generate click data")
        print("3. Make test donations to generate donation data")
        print("4. View real analytics at /admin/analytics")
        
    except Exception as e:
        print(f"❌ Error clearing sample data: {str(e)}")
        db.rollback()
        return False
    
    finally:
        db.close()
    
    return True

if __name__ == "__main__":
    print("Clearing sample analytics data...")
    success = clear_sample_data()
    if not success:
        print("\n❌ Failed to clear sample data!")
        sys.exit(1)
