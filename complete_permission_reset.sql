-- Complete PostgreSQL Permission Reset for Hello Cyril
-- Run this as postgres superuser to completely fix all permission issues

-- Connect to the cyril_security database
\c cyril_security;

-- Show current user to confirm we're postgres
SELECT 'Running as user: ' || current_user as status;

-- First, let's see what's causing the permission issues
SELECT 'Current database and user info:' as info;
SELECT current_database() as database, current_user as user, session_user as session_user;

-- Check if hellocyril user exists
SELECT 'Checking if hellocyril user exists:' as info;
SELECT usename, usecreatedb, usesuper FROM pg_user WHERE usename IN ('postgres', 'hellocyril');

-- Create hellocyril user if it doesn't exist (with proper permissions)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_user WHERE usename = 'hellocyril') THEN
        CREATE USER hellocyril WITH PASSWORD 'local';
        ALTER USER hellocyril CREATEDB;
        RAISE NOTICE 'Created hellocyril user';
    ELSE
        RAISE NOTICE 'hellocyril user already exists';
    END IF;
END $$;

-- Make sure postgres is superuser and hellocyril has necessary privileges
ALTER USER postgres WITH SUPERUSER;
ALTER USER hellocyril WITH CREATEDB;

-- Grant hellocyril the ability to create roles (needed for some operations)
ALTER USER hellocyril WITH CREATEROLE;

-- Now let's fix the database-level permissions
GRANT ALL PRIVILEGES ON DATABASE cyril_security TO postgres;
GRANT ALL PRIVILEGES ON DATABASE cyril_security TO hellocyril;

-- Connect to the database and fix schema permissions
GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON SCHEMA public TO hellocyril;
GRANT USAGE ON SCHEMA public TO postgres;
GRANT USAGE ON SCHEMA public TO hellocyril;
GRANT CREATE ON SCHEMA public TO postgres;
GRANT CREATE ON SCHEMA public TO hellocyril;

-- Show all tables and their current owners
SELECT 'Current table ownership before changes:' as info;
SELECT schemaname, tablename, tableowner 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Force ownership change of ALL objects to postgres first (as superuser)
-- This should work since we're postgres superuser
DO $$
DECLARE
    table_record RECORD;
    seq_record RECORD;
    view_record RECORD;
BEGIN
    -- Change table ownership
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE 'ALTER TABLE public.' || quote_ident(table_record.tablename) || ' OWNER TO postgres';
            RAISE NOTICE 'Changed table % ownership to postgres', table_record.tablename;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Failed to change table % ownership: %', table_record.tablename, SQLERRM;
        END;
    END LOOP;
    
    -- Change sequence ownership
    FOR seq_record IN 
        SELECT sequencename 
        FROM pg_sequences 
        WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE 'ALTER SEQUENCE public.' || quote_ident(seq_record.sequencename) || ' OWNER TO postgres';
            RAISE NOTICE 'Changed sequence % ownership to postgres', seq_record.sequencename;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Failed to change sequence % ownership: %', seq_record.sequencename, SQLERRM;
        END;
    END LOOP;
    
    -- Change view ownership if any
    FOR view_record IN 
        SELECT viewname 
        FROM pg_views 
        WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE 'ALTER VIEW public.' || quote_ident(view_record.viewname) || ' OWNER TO postgres';
            RAISE NOTICE 'Changed view % ownership to postgres', view_record.viewname;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Failed to change view % ownership: %', view_record.viewname, SQLERRM;
        END;
    END LOOP;
END $$;

-- Now grant ALL privileges on ALL objects to both users
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hellocyril;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hellocyril;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO hellocyril;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO hellocyril;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO hellocyril;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO hellocyril;

-- Now change ownership to hellocyril (the application user)
DO $$
DECLARE
    table_record RECORD;
    seq_record RECORD;
BEGIN
    -- Change table ownership to hellocyril
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE 'ALTER TABLE public.' || quote_ident(table_record.tablename) || ' OWNER TO hellocyril';
            RAISE NOTICE 'Changed table % ownership to hellocyril', table_record.tablename;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Failed to change table % ownership to hellocyril: %', table_record.tablename, SQLERRM;
        END;
    END LOOP;
    
    -- Change sequence ownership to hellocyril
    FOR seq_record IN 
        SELECT sequencename 
        FROM pg_sequences 
        WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE 'ALTER SEQUENCE public.' || quote_ident(seq_record.sequencename) || ' OWNER TO hellocyril';
            RAISE NOTICE 'Changed sequence % ownership to hellocyril', seq_record.sequencename;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Failed to change sequence % ownership to hellocyril: %', seq_record.sequencename, SQLERRM;
        END;
    END LOOP;
END $$;

-- Show final table ownership
SELECT 'Final table ownership after changes:' as info;
SELECT schemaname, tablename, tableowner 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Test permissions for both users on critical tables
SELECT 'Testing permissions for postgres user:' as info;
SELECT 
    'subscription_payments' as table_name,
    has_table_privilege('postgres', 'subscription_payments', 'SELECT') as postgres_select,
    has_table_privilege('postgres', 'subscription_payments', 'INSERT') as postgres_insert,
    has_table_privilege('postgres', 'subscription_payments', 'UPDATE') as postgres_update,
    has_table_privilege('postgres', 'subscription_payments', 'DELETE') as postgres_delete
WHERE EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'subscription_payments');

SELECT 'Testing permissions for hellocyril user:' as info;
SELECT 
    'subscription_payments' as table_name,
    has_table_privilege('hellocyril', 'subscription_payments', 'SELECT') as hellocyril_select,
    has_table_privilege('hellocyril', 'subscription_payments', 'INSERT') as hellocyril_insert,
    has_table_privilege('hellocyril', 'subscription_payments', 'UPDATE') as hellocyril_update,
    has_table_privilege('hellocyril', 'subscription_payments', 'DELETE') as hellocyril_delete
WHERE EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'subscription_payments');

-- Test actual access to the problematic table
SELECT 'Testing actual access to subscription_payments:' as info;
BEGIN;
    SELECT COUNT(*) as subscription_payments_count FROM subscription_payments;
    SELECT COUNT(*) as subscribers_count FROM subscribers;
    SELECT COUNT(*) as api_keys_count FROM api_keys;
COMMIT;

-- Final verification - show all permissions
SELECT 'All table permissions summary:' as info;
SELECT 
    t.tablename,
    t.tableowner,
    has_table_privilege('postgres', t.tablename, 'SELECT') as postgres_can_select,
    has_table_privilege('hellocyril', t.tablename, 'SELECT') as hellocyril_can_select
FROM pg_tables t 
WHERE t.schemaname = 'public' 
ORDER BY t.tablename;

-- Success message
SELECT '🎉 Permission reset completed!' as result;
SELECT 'Both postgres and hellocyril users should now have full access to all tables' as instruction;
