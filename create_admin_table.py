#!/usr/bin/env python3
"""
Migration script to create the admins table and add a default admin user
"""

import sys
import os
from sqlalchemy import text

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.models.database import get_db
from app.models.admin import Admin

def create_admin_table():
    """Create the admins table"""
    
    db = next(get_db())
    
    try:
        # Check if table already exists
        result = db.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name = 'admins'
        """))
        
        if result.fetchone():
            print("✅ admins table already exists")
        else:
            # Create the admins table
            print("Creating admins table...")
            db.execute(text("""
                CREATE TABLE admins (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                    last_login TIMESTAMP
                );
                
                CREATE INDEX idx_admins_username ON admins(username);
                CREATE INDEX idx_admins_email ON admins(email);
            """))
            
            db.commit()
            print("✅ Successfully created admins table")
        
        # Check if default admin exists
        result = db.execute(text("""
            SELECT username FROM admins WHERE username = 'admin'
        """))
        
        if result.fetchone():
            print("✅ Default admin user already exists")
        else:
            # Create default admin user
            print("Creating default admin user...")
            admin = Admin(
                username="admin",
                email="<EMAIL>"
            )
            admin.set_password("admin123")  # Change this password!
            
            db.add(admin)
            db.commit()
            print("✅ Successfully created default admin user")
            print("   Username: admin")
            print("   Password: admin123")
            print("   ⚠️  IMPORTANT: Change this password after first login!")
        
    except Exception as e:
        print(f"❌ Error running migration: {str(e)}")
        db.rollback()
        return False
    
    finally:
        db.close()
    
    return True

if __name__ == "__main__":
    print("Creating admin table and default user...")
    success = create_admin_table()
    if success:
        print("\n🎉 Admin setup completed successfully!")
        print("\nYou can now:")
        print("1. Start the application")
        print("2. Go to /admin/login")
        print("3. Login with username: admin, password: admin123")
        print("4. Change the default password immediately!")
    else:
        print("\n❌ Admin setup failed!")
        sys.exit(1)
