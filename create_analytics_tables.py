#!/usr/bin/env python3
"""
Migration script to create the analytics tables for tracking page visits, WhatsApp clicks, and donations
"""

import sys
import os
from sqlalchemy import text

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.models.database import get_db

def create_analytics_tables():
    """Create the analytics tables"""

    db = next(get_db())

    try:
        print("Creating analytics tables...")

        # Create page_visits table
        print("Creating page_visits table...")
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS page_visits (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                page_url VARCHAR(500) NOT NULL,
                page_title VARCHAR(200),
                referrer VARCHAR(500),
                visit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                session_id VARCHAR(100),
                country VARCHAR(100),
                city VARCHAR(100)
            );

            CREATE INDEX IF NOT EXISTS idx_page_visits_ip ON page_visits(ip_address);
            CREATE INDEX IF NOT EXISTS idx_page_visits_time ON page_visits(visit_time);
            CREATE INDEX IF NOT EXISTS idx_page_visits_url ON page_visits(page_url);
        """))

        # Create whatsapp_clicks table
        print("Creating whatsapp_clicks table...")
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS whatsapp_clicks (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                click_source VARCHAR(100) NOT NULL,
                whatsapp_number VARCHAR(20),
                click_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                session_id VARCHAR(100),
                referrer_page VARCHAR(500),
                country VARCHAR(100),
                city VARCHAR(100)
            );

            CREATE INDEX IF NOT EXISTS idx_whatsapp_clicks_ip ON whatsapp_clicks(ip_address);
            CREATE INDEX IF NOT EXISTS idx_whatsapp_clicks_time ON whatsapp_clicks(click_time);
            CREATE INDEX IF NOT EXISTS idx_whatsapp_clicks_source ON whatsapp_clicks(click_source);
        """))

        # Create donation_tracking table
        print("Creating donation_tracking table...")
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS donation_tracking (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                amount FLOAT NOT NULL,
                currency VARCHAR(3) DEFAULT 'ZAR' NOT NULL,
                payment_method VARCHAR(50) NOT NULL,
                payment_status VARCHAR(20) NOT NULL,
                transaction_id VARCHAR(100),
                payment_provider_id VARCHAR(100),
                donation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                completion_time TIMESTAMP,
                session_id VARCHAR(100),
                country VARCHAR(100),
                city VARCHAR(100),
                notes TEXT
            );

            CREATE INDEX IF NOT EXISTS idx_donation_tracking_ip ON donation_tracking(ip_address);
            CREATE INDEX IF NOT EXISTS idx_donation_tracking_time ON donation_tracking(donation_time);
            CREATE INDEX IF NOT EXISTS idx_donation_tracking_status ON donation_tracking(payment_status);
            CREATE INDEX IF NOT EXISTS idx_donation_tracking_amount ON donation_tracking(amount);
        """))

        db.commit()
        print("✅ Successfully created all analytics tables")

        # Insert some sample data for testing
        print("Inserting sample analytics data...")

        # Sample page visits
        db.execute(text("""
            INSERT INTO page_visits (id, ip_address, user_agent, page_url, page_title, referrer, visit_time)
            VALUES
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '/', 'Hello Cyril - Home', 'https://google.com', NOW() - INTERVAL '2 hours'),
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', '/', 'Hello Cyril - Home', NULL, NOW() - INTERVAL '1 hour'),
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (Android 10; Mobile)', '/feedback', 'Feedback - Hello Cyril', '/', NOW() - INTERVAL '30 minutes')
            ON CONFLICT DO NOTHING;
        """))

        # Sample WhatsApp clicks
        db.execute(text("""
            INSERT INTO whatsapp_clicks (id, ip_address, user_agent, click_source, whatsapp_number, referrer_page, click_time)
            VALUES
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'navbar', '+27123456789', '/', NOW() - INTERVAL '1 hour'),
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', 'hero', '+27123456789', '/', NOW() - INTERVAL '45 minutes'),
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (Android 10; Mobile)', 'contact-section', '+27123456789', '/', NOW() - INTERVAL '20 minutes')
            ON CONFLICT DO NOTHING;
        """))

        # Sample donations
        db.execute(text("""
            INSERT INTO donation_tracking (id, ip_address, user_agent, amount, currency, payment_method, payment_status, transaction_id, donation_time, completion_time)
            VALUES
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 100.00, 'ZAR', 'yoco', 'completed', 'TXN123456', NOW() - INTERVAL '3 hours', NOW() - INTERVAL '3 hours'),
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', 50.00, 'ZAR', 'yoco', 'completed', 'TXN123457', NOW() - INTERVAL '2 hours', NOW() - INTERVAL '2 hours'),
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (Android 10; Mobile)', 25.00, 'ZAR', 'yoco', 'failed', 'TXN123458', NOW() - INTERVAL '1 hour', NULL),
                (gen_random_uuid(), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', 200.00, 'ZAR', 'yoco', 'initiated', 'TXN123459', NOW() - INTERVAL '10 minutes', NULL)
            ON CONFLICT DO NOTHING;
        """))

        db.commit()
        print("✅ Successfully inserted sample analytics data")

    except Exception as e:
        print(f"❌ Error creating analytics tables: {str(e)}")
        db.rollback()
        return False

    finally:
        db.close()

    return True

if __name__ == "__main__":
    print("Creating analytics tables...")
    success = create_analytics_tables()
    if success:
        print("\n🎉 Analytics setup completed successfully!")
        print("\nAnalytics features now available:")
        print("1. Page visit tracking")
        print("2. WhatsApp click tracking")
        print("3. Donation tracking")
        print("4. Admin analytics dashboard at /admin/analytics")
        print("\nTo enable tracking on your website:")
        print("1. Include the analytics.js script in your templates")
        print("2. The script will automatically track page visits and WhatsApp clicks")
        print("3. Use the provided methods to track donations")
    else:
        print("\n❌ Analytics setup failed!")
        sys.exit(1)
