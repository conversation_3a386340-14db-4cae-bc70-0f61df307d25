#!/usr/bin/env python3
"""
Deployment script for security enhancements and subscription service
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr}")
        return False

def check_environment():
    """Check if we're in the right environment"""
    print("🔍 Checking environment...")

    # Check if we're in the hello_cyril directory
    if not os.path.exists("app/main.py"):
        print("❌ Please run this script from the hello_cyril root directory")
        return False

    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or python_version.minor < 8:
        print("❌ Python 3.8+ required")
        return False

    print("✅ Environment check passed")
    return True

def install_dependencies():
    """Install new dependencies"""
    print("📦 Installing dependencies...")

    # Install main requirements
    if not run_command("pip install -r requirements.txt", "Installing main requirements"):
        return False

    # Install subscription requirements
    if not run_command("pip install -r requirements_subscription.txt", "Installing subscription requirements"):
        return False

    return True

def create_database_tables():
    """Create new database tables"""
    print("🗄️ Creating database tables...")

    script = """
from app.models.database import engine, Base
from app.models import subscription
import sys

try:
    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("SUCCESS: Database tables created successfully")
    sys.exit(0)
except Exception as e:
    print(f"ERROR: Database table creation failed: {str(e)}")
    sys.exit(1)
"""

    with open("temp_db_setup.py", "w", encoding="utf-8") as f:
        f.write(script)

    success = run_command("python temp_db_setup.py", "Creating database tables")

    # Clean up
    if os.path.exists("temp_db_setup.py"):
        os.remove("temp_db_setup.py")

    return success

def setup_environment_variables():
    """Setup environment variables"""
    print("🔧 Setting up environment variables...")

    env_vars = {
        "ENVIRONMENT": "production",
        "REDIS_URL": "redis://localhost:6379",
        "SECRET_KEY": "your-secure-secret-key-change-this",
    }

    env_file = ".env"
    existing_vars = {}

    # Read existing .env file
    if os.path.exists(env_file):
        with open(env_file, "r", encoding="utf-8") as f:
            for line in f:
                if "=" in line and not line.startswith("#"):
                    key, value = line.strip().split("=", 1)
                    existing_vars[key] = value

    # Add new variables if they don't exist
    new_vars = []
    for key, default_value in env_vars.items():
        if key not in existing_vars:
            new_vars.append(f"{key}={default_value}")

    if new_vars:
        with open(env_file, "a", encoding="utf-8") as f:
            f.write("\n# Subscription service variables\n")
            for var in new_vars:
                f.write(f"{var}\n")
        print(f"✅ Added {len(new_vars)} new environment variables to .env")
    else:
        print("✅ Environment variables already configured")

    return True

def test_security_middleware():
    """Test if security middleware is working"""
    print("🛡️ Testing security middleware...")

    test_script = """
import sys
try:
    from app.middleware.security import APISecurityMiddleware, InputSanitizationMiddleware
    print("SUCCESS: Security middleware imported successfully")
    sys.exit(0)
except ImportError as e:
    print(f"ERROR: Security middleware import failed: {str(e)}")
    sys.exit(1)
"""

    with open("temp_security_test.py", "w", encoding="utf-8") as f:
        f.write(test_script)

    success = run_command("python temp_security_test.py", "Testing security middleware")

    # Clean up
    if os.path.exists("temp_security_test.py"):
        os.remove("temp_security_test.py")

    return success

def test_subscription_api():
    """Test if subscription API is working"""
    print("🔑 Testing subscription API...")

    test_script = """
import sys
try:
    from app.api.subscription import router
    from app.models.subscription import Subscriber, APIKey
    from app.schemas.subscription import SubscriberCreate
    print("SUCCESS: Subscription API imported successfully")
    sys.exit(0)
except ImportError as e:
    print(f"ERROR: Subscription API import failed: {str(e)}")
    sys.exit(1)
"""

    with open("temp_subscription_test.py", "w", encoding="utf-8") as f:
        f.write(test_script)

    success = run_command("python temp_subscription_test.py", "Testing subscription API")

    # Clean up
    if os.path.exists("temp_subscription_test.py"):
        os.remove("temp_subscription_test.py")

    return success

def create_deployment_summary():
    """Create deployment summary"""
    summary = """
🎉 DEPLOYMENT SUMMARY
====================

✅ Security Enhancements Deployed:
   - Enhanced rate limiting middleware
   - Input sanitization (SQL injection & XSS protection)
   - IP blocking and suspicious activity detection
   - Test endpoint protection in production

✅ Subscription Service Deployed:
   - Three-tier subscription model (Basic/Premium/Enterprise)
   - API key authentication system
   - Usage tracking and billing
   - Real-time alert system (Redis required)

🔧 NEXT STEPS FOR LIVE SERVER:
=============================

1. Install Redis:
   sudo apt update
   sudo apt install redis-server
   sudo systemctl start redis-server
   sudo systemctl enable redis-server

2. Update environment variables in .env:
   ENVIRONMENT=production
   REDIS_URL=redis://localhost:6379
   SECRET_KEY=your-very-secure-secret-key

3. Restart your application:
   sudo systemctl restart apache2
   # or your specific service

4. Test the deployment:
   curl https://hellocyril.co.za/api/subscription/subscribe

📊 SUBSCRIPTION PLANS:
=====================
- Basic: R99/month, 10K requests
- Premium: R299/month, 50K requests, real-time alerts
- Enterprise: R999/month, 200K requests, webhooks

🔒 SECURITY FEATURES:
====================
- Rate limiting: 100 API requests/hour, 1000 public/hour
- Input validation: SQL injection & XSS protection
- IP blocking: Automatic suspicious activity detection
- Test endpoints: Blocked in production environment

📈 REVENUE POTENTIAL:
====================
Conservative: R4,483/month (R53,796/year)
Growth scenario: R15,445/month (R185,340/year)
"""

    print(summary)

    # Save to file
    with open("deployment_summary.txt", "w", encoding="utf-8") as f:
        f.write(summary)

    print("📄 Deployment summary saved to deployment_summary.txt")

def main():
    """Main deployment function"""
    print("🚀 Hello Cyril Security & Subscription Deployment")
    print("=" * 50)

    # Check environment
    if not check_environment():
        return False

    # Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed")
        return False

    # Create database tables
    if not create_database_tables():
        print("❌ Database setup failed")
        return False

    # Setup environment variables
    if not setup_environment_variables():
        print("❌ Environment setup failed")
        return False

    # Test security middleware
    if not test_security_middleware():
        print("❌ Security middleware test failed")
        return False

    # Test subscription API
    if not test_subscription_api():
        print("❌ Subscription API test failed")
        return False

    # Create summary
    create_deployment_summary()

    print("\n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!")
    print("📋 Check deployment_summary.txt for next steps")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
