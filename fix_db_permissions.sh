#!/bin/bash

# Hello Cyril - Database Permissions Fix Script
# Run this on your Ubuntu server to fix PostgreSQL permissions

echo "🚀 Hello Cyril - Database Permissions Fix"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}🔧 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root. This is fine for fixing permissions."
elif sudo -n true 2>/dev/null; then
    print_success "Sudo access available"
else
    print_error "This script needs sudo access to fix PostgreSQL permissions"
    echo "Please run with: sudo bash fix_db_permissions.sh"
    exit 1
fi

# Check if PostgreSQL is running
print_status "Checking PostgreSQL service..."
if systemctl is-active --quiet postgresql; then
    print_success "PostgreSQL is running"
else
    print_status "Starting PostgreSQL service..."
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    print_success "PostgreSQL started"
fi

# Function to run SQL as postgres user
run_sql_as_postgres() {
    local sql_command="$1"
    local description="$2"
    
    print_status "$description"
    
    if sudo -u postgres psql -d cyril_security -c "$sql_command" 2>/dev/null; then
        print_success "$description - completed"
        return 0
    else
        print_error "$description - failed"
        return 1
    fi
}

# Function to test table access
test_table_access() {
    local table_name="$1"
    local user="$2"
    
    if sudo -u postgres psql -d cyril_security -c "SELECT COUNT(*) FROM $table_name;" >/dev/null 2>&1; then
        local count=$(sudo -u postgres psql -d cyril_security -t -c "SELECT COUNT(*) FROM $table_name;" | tr -d ' ')
        print_success "$table_name: $count records - ACCESSIBLE"
        return 0
    else
        print_error "$table_name: NOT ACCESSIBLE"
        return 1
    fi
}

print_status "Starting database permissions fix..."

# Step 1: Grant basic privileges
print_status "Step 1: Granting database and schema privileges..."

run_sql_as_postgres "GRANT ALL PRIVILEGES ON DATABASE cyril_security TO postgres;" "Grant database privileges to postgres"
run_sql_as_postgres "GRANT ALL PRIVILEGES ON DATABASE cyril_security TO hellocyril;" "Grant database privileges to hellocyril"
run_sql_as_postgres "GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;" "Grant schema privileges to postgres"
run_sql_as_postgres "GRANT ALL PRIVILEGES ON SCHEMA public TO hellocyril;" "Grant schema privileges to hellocyril"

# Step 2: Grant table and sequence privileges
print_status "Step 2: Granting table and sequence privileges..."

run_sql_as_postgres "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;" "Grant table privileges to postgres"
run_sql_as_postgres "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hellocyril;" "Grant table privileges to hellocyril"
run_sql_as_postgres "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;" "Grant sequence privileges to postgres"
run_sql_as_postgres "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hellocyril;" "Grant sequence privileges to hellocyril"

# Step 3: Set default privileges for future objects
print_status "Step 3: Setting default privileges for future objects..."

run_sql_as_postgres "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres;" "Set default table privileges for postgres"
run_sql_as_postgres "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO hellocyril;" "Set default table privileges for hellocyril"
run_sql_as_postgres "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;" "Set default sequence privileges for postgres"
run_sql_as_postgres "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO hellocyril;" "Set default sequence privileges for hellocyril"

# Step 4: Change ownership of all tables to postgres
print_status "Step 4: Changing table ownership to postgres..."

# Get list of tables and change ownership
sudo -u postgres psql -d cyril_security -c "
DO \$\$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ALTER TABLE public.' || quote_ident(table_record.tablename) || ' OWNER TO postgres';
        RAISE NOTICE 'Changed ownership of table % to postgres', table_record.tablename;
    END LOOP;
END \$\$;
" 2>/dev/null

if [ $? -eq 0 ]; then
    print_success "Changed table ownership to postgres"
else
    print_warning "Some tables may not have changed ownership"
fi

# Step 5: Change ownership of all sequences to postgres
print_status "Step 5: Changing sequence ownership to postgres..."

sudo -u postgres psql -d cyril_security -c "
DO \$\$
DECLARE
    seq_record RECORD;
BEGIN
    FOR seq_record IN 
        SELECT sequencename 
        FROM pg_sequences 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ALTER SEQUENCE public.' || quote_ident(seq_record.sequencename) || ' OWNER TO postgres';
        RAISE NOTICE 'Changed ownership of sequence % to postgres', seq_record.sequencename;
    END LOOP;
END \$\$;
" 2>/dev/null

if [ $? -eq 0 ]; then
    print_success "Changed sequence ownership to postgres"
else
    print_warning "Some sequences may not have changed ownership"
fi

# Step 6: Test access to subscription tables
print_status "Step 6: Testing access to subscription tables..."

subscription_tables=("subscription_payments" "subscribers" "api_keys" "alert_subscriptions" "api_usage_logs")
all_accessible=true

for table in "${subscription_tables[@]}"; do
    if ! test_table_access "$table" "postgres"; then
        all_accessible=false
    fi
done

# Step 7: Show final table ownership
print_status "Step 7: Showing final table ownership..."

echo "Table ownership:"
sudo -u postgres psql -d cyril_security -c "
SELECT schemaname, tablename, tableowner 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;
" 2>/dev/null | grep -E "(schemaname|public)" || print_warning "Could not display table ownership"

# Final results
echo ""
echo "=========================================="
if [ "$all_accessible" = true ]; then
    print_success "🎉 ALL PERMISSIONS FIXED SUCCESSFULLY!"
    echo ""
    echo "📋 You can now:"
    echo "   ✅ Access all tables in your PostgreSQL GUI"
    echo "   ✅ Edit subscription data without permission errors"
    echo "   ✅ Run queries on all tables"
    echo "   ✅ Use the Hello Cyril API system fully"
    echo ""
    echo "🔄 Next steps:"
    echo "   1. Refresh your PostgreSQL GUI connection"
    echo "   2. Try accessing the subscription_payments table"
    echo "   3. Test editing data in the subscription tables"
else
    print_warning "Some tables may still have permission issues"
    echo ""
    echo "🛠️ Manual fix commands (run these if needed):"
    echo "   sudo -u postgres psql"
    echo "   \\c cyril_security;"
    echo "   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;"
    echo "   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hellocyril;"
    echo "   \\q"
fi

echo "=========================================="

# Optional: Create a test script for future verification
print_status "Creating test script for future verification..."

cat > /home/<USER>/test_db_access.py << 'EOF'
#!/usr/bin/env python3
import psycopg2

def test_db_access():
    try:
        conn = psycopg2.connect(
            dbname='cyril_security',
            user='hellocyril',
            password='local',
            host='localhost'
        )
        cur = conn.cursor()
        
        # Test subscription tables
        tables = ['subscription_payments', 'subscribers', 'api_keys', 'alert_subscriptions', 'api_usage_logs']
        
        print("🧪 Testing database access...")
        all_good = True
        
        for table in tables:
            try:
                cur.execute(f"SELECT COUNT(*) FROM {table};")
                count = cur.fetchone()[0]
                print(f"✅ {table}: {count} records")
            except Exception as e:
                print(f"❌ {table}: {e}")
                all_good = False
        
        cur.close()
        conn.close()
        
        if all_good:
            print("🎉 All database access tests passed!")
        else:
            print("⚠️ Some database access issues remain")
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")

if __name__ == "__main__":
    test_db_access()
EOF

chmod +x /home/<USER>/test_db_access.py
chown hellocyril:hellocyril /home/<USER>/test_db_access.py

print_success "Created test script: /home/<USER>/test_db_access.py"
print_status "You can run 'python3 /home/<USER>/test_db_access.py' to test access anytime"

echo ""
print_success "Database permissions fix completed!"
