-- Hello Cyril - PostgreSQL Permissions Fix
-- Run this script as postgres superuser to fix table permissions

-- Connect to the cyril_security database first
\c cyril_security;

-- Show current user
SELECT 'Current user: ' || current_user;

-- Show all tables in the database
SELECT 'Tables in database:' as info;
SELECT schemaname, tablename, tableowner 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Grant all privileges to postgres user (for local development)
GRANT ALL PRIVILEGES ON DATABASE cyril_security TO postgres;
GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;

-- If you're using hellocyril user (production), grant to that user too
-- Uncomment these lines if needed:
-- GRANT ALL PRIVILEGES ON DATABASE cyril_security TO hellocyril;
-- GRANT ALL PRIVILEGES ON SCHEMA public TO hellocyril;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hellocyril;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hellocyril;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO hellocyril;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO hellocyril;

-- Change ownership of all tables to postgres (for local development)
-- This ensures you have full control over all tables

-- Get list of tables and change ownership
DO $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ALTER TABLE public.' || quote_ident(table_record.tablename) || ' OWNER TO postgres';
        RAISE NOTICE 'Changed ownership of table % to postgres', table_record.tablename;
    END LOOP;
END $$;

-- Change ownership of all sequences to postgres
DO $$
DECLARE
    seq_record RECORD;
BEGIN
    FOR seq_record IN 
        SELECT sequencename 
        FROM pg_sequences 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ALTER SEQUENCE public.' || quote_ident(seq_record.sequencename) || ' OWNER TO hellocyril';
        RAISE NOTICE 'Changed ownership of sequence % to postgres', seq_record.sequencename;
    END LOOP;
END $$;

-- Verify permissions on subscription tables
SELECT 'Checking permissions on subscription tables:' as info;

SELECT 
    'subscription_payments' as table_name,
    has_table_privilege('postgres', 'subscription_payments', 'SELECT') as can_select,
    has_table_privilege('postgres', 'subscription_payments', 'INSERT') as can_insert,
    has_table_privilege('postgres', 'subscription_payments', 'UPDATE') as can_update,
    has_table_privilege('postgres', 'subscription_payments', 'DELETE') as can_delete
WHERE EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'subscription_payments');

SELECT 
    'subscribers' as table_name,
    has_table_privilege('postgres', 'subscribers', 'SELECT') as can_select,
    has_table_privilege('postgres', 'subscribers', 'INSERT') as can_insert,
    has_table_privilege('postgres', 'subscribers', 'UPDATE') as can_update,
    has_table_privilege('postgres', 'subscribers', 'DELETE') as can_delete
WHERE EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'subscribers');

SELECT 
    'api_keys' as table_name,
    has_table_privilege('postgres', 'api_keys', 'SELECT') as can_select,
    has_table_privilege('postgres', 'api_keys', 'INSERT') as can_insert,
    has_table_privilege('postgres', 'api_keys', 'UPDATE') as can_update,
    has_table_privilege('postgres', 'api_keys', 'DELETE') as can_delete
WHERE EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'api_keys');

SELECT 
    'alert_subscriptions' as table_name,
    has_table_privilege('postgres', 'alert_subscriptions', 'SELECT') as can_select,
    has_table_privilege('postgres', 'alert_subscriptions', 'INSERT') as can_insert,
    has_table_privilege('postgres', 'alert_subscriptions', 'UPDATE') as can_update,
    has_table_privilege('postgres', 'alert_subscriptions', 'DELETE') as can_delete
WHERE EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'alert_subscriptions');

SELECT 
    'api_usage_logs' as table_name,
    has_table_privilege('postgres', 'api_usage_logs', 'SELECT') as can_select,
    has_table_privilege('postgres', 'api_usage_logs', 'INSERT') as can_insert,
    has_table_privilege('postgres', 'api_usage_logs', 'UPDATE') as can_update,
    has_table_privilege('postgres', 'api_usage_logs', 'DELETE') as can_delete
WHERE EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'api_usage_logs');

-- Test access to subscription_payments table
SELECT 'Testing access to subscription_payments table:' as info;
SELECT COUNT(*) as record_count FROM subscription_payments;

-- Show final table ownership
SELECT 'Final table ownership:' as info;
SELECT schemaname, tablename, tableowner 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Success message
SELECT '✅ Permissions fix completed successfully!' as result;
SELECT 'You should now be able to access all tables in your PostgreSQL GUI' as instruction;
