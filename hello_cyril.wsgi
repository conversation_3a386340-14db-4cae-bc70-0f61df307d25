#!/usr/bin/python3
import sys
import os

# Add project directory to Python path
project_dir = '/home/<USER>/hello_cyril'
if project_dir not in sys.path:
    sys.path.insert(0, project_dir)

os.environ['PYTHONPATH'] = '/home/<USER>/hello_cyril'

# Set working directory
os.chdir(project_dir)

# Set environment variables directly in WSGI
os.environ['SQLALCHEMY_DATABASE_URL'] = 'postgresql://hellocyril:local@localhost/cyril_security'
os.environ['WHATSAPP_NUMBER'] = '+***********'
os.environ['TWILIO_ACCOUNT_SID'] = '**********************************'
os.environ['TWILIO_AUTH_TOKEN'] = 'a62c5269b75ad5d69e321d09c315b023'
os.environ['TWILIO_FROM_NUMBER'] = 'whatsapp:+***********'
os.environ['SECRET_KEY'] = 'aSLKFsKFDKdjfkJMKLDFMFKLlasjdlkfajsoidiwernJJSkasdjlkkkj'
os.environ['DEBUG'] = 'False'
os.environ['TWILIO_TEMPLATE_SID'] = 'HXc439ec5bca9dfa5737add3120373891b'
os.environ['TWILIO_NOTIFICATION_TEMPLATE_SID'] = "HX0a98e8081bbb3a01f407ae0eb0d4ed1c"
os.environ['WHATSAPP_API_URL'] = 'https://api.twilio.com/2010-04-01/Accounts/'
os.environ['ALLOWED_HOSTS'] = 'hellocyril.co.za,www.hellocyril.co.za,localhost,127.0.0.1'

os.environ['REDIS_URL'] ="redis://localhost:6379"
os.environ['ENVIRONMENT'] ="production"  # Blocks test endpoints
os.environ['SECRET_KEY'] ="JSHhsd3423Jshfask87Jjksds23ndk9wednnnwu8wesd9sd9sd099saassdn"

os.environ['YOCO_SECRET_KEY'] = '************************************'

# Import FastAPI app and convert to WSGI
from app.main import app
from a2wsgi import ASGIMiddleware

# Convert FastAPI (ASGI) to WSGI
application = ASGIMiddleware(app)
