#!/bin/bash

# Complete Database Permission Reset for Hello Cyril
# This script will completely reset and fix all PostgreSQL permissions

echo "🚀 Hello Cyril - Complete Database Permission Reset"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}🔧 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# Check if running with proper permissions
if [[ $EUID -eq 0 ]]; then
    print_success "Running as root - good for fixing permissions"
elif sudo -n true 2>/dev/null; then
    print_success "Sudo access available"
else
    print_error "This script needs sudo access"
    echo "Please run: sudo bash reset_db_permissions.sh"
    exit 1
fi

# Stop the application first to avoid conflicts
print_status "Stopping Hello Cyril application..."
sudo systemctl stop hello_cyril 2>/dev/null || print_warning "Hello Cyril service not running or doesn't exist"

# Restart PostgreSQL to clear any locks
print_status "Restarting PostgreSQL to clear any locks..."
sudo systemctl restart postgresql
sleep 3

if ! sudo systemctl is-active --quiet postgresql; then
    print_error "PostgreSQL failed to start"
    exit 1
fi

print_success "PostgreSQL is running"

# Method 1: Complete reset using SQL script
print_status "Method 1: Running complete permission reset SQL script..."

# Create a temporary SQL script with all the commands
cat > /tmp/reset_permissions.sql << 'EOF'
-- Complete permission reset
\c cyril_security;

-- Ensure we're running as postgres superuser
SELECT 'Running as: ' || current_user;

-- Create hellocyril user if doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_user WHERE usename = 'hellocyril') THEN
        CREATE USER hellocyril WITH PASSWORD 'local';
        ALTER USER hellocyril CREATEDB CREATEROLE;
        RAISE NOTICE 'Created hellocyril user';
    ELSE
        ALTER USER hellocyril WITH CREATEDB CREATEROLE;
        RAISE NOTICE 'Updated hellocyril user permissions';
    END IF;
END $$;

-- Grant database and schema permissions
GRANT ALL PRIVILEGES ON DATABASE cyril_security TO postgres;
GRANT ALL PRIVILEGES ON DATABASE cyril_security TO hellocyril;
GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON SCHEMA public TO hellocyril;

-- Force change ownership of all tables to postgres first (as superuser)
DO $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE 'ALTER TABLE public.' || quote_ident(table_record.tablename) || ' OWNER TO postgres';
            RAISE NOTICE 'Changed % to postgres ownership', table_record.tablename;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not change % ownership: %', table_record.tablename, SQLERRM;
        END;
    END LOOP;
END $$;

-- Grant all privileges on all objects
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hellocyril;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hellocyril;

-- Set default privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO hellocyril;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO hellocyril;

-- Change ownership to hellocyril (application user)
DO $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN 
        SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE 'ALTER TABLE public.' || quote_ident(table_record.tablename) || ' OWNER TO hellocyril';
            RAISE NOTICE 'Changed % to hellocyril ownership', table_record.tablename;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Could not change % to hellocyril: %', table_record.tablename, SQLERRM;
        END;
    END LOOP;
END $$;

-- Test access
SELECT 'Testing access...' as status;
SELECT COUNT(*) as subscription_payments_count FROM subscription_payments;
SELECT 'Permission reset completed successfully!' as result;
EOF

# Run the SQL script as postgres superuser
if sudo -u postgres psql -f /tmp/reset_permissions.sql; then
    print_success "SQL permission reset completed"
else
    print_warning "SQL method had some issues, trying alternative method..."
fi

# Method 2: Direct psql commands
print_status "Method 2: Running direct psql commands..."

# Run individual commands to ensure they work
sudo -u postgres psql -d cyril_security -c "
    -- Ensure hellocyril user exists with proper permissions
    DO \$\$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_user WHERE usename = 'hellocyril') THEN
            CREATE USER hellocyril WITH PASSWORD 'local';
        END IF;
        ALTER USER hellocyril WITH CREATEDB CREATEROLE;
    END \$\$;
    
    -- Grant all privileges
    GRANT ALL PRIVILEGES ON DATABASE cyril_security TO hellocyril;
    GRANT ALL PRIVILEGES ON SCHEMA public TO hellocyril;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hellocyril;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hellocyril;
"

if [ $? -eq 0 ]; then
    print_success "Direct psql commands completed"
else
    print_warning "Some direct commands failed"
fi

# Method 3: System-level permission fix
print_status "Method 3: System-level fixes..."

# Change file ownership of PostgreSQL data directory if needed
sudo chown -R postgres:postgres /var/lib/postgresql/ 2>/dev/null || print_warning "Could not change PostgreSQL directory ownership"

# Restart PostgreSQL again
sudo systemctl restart postgresql
sleep 2

# Method 4: Test access as hellocyril user
print_status "Method 4: Testing access as hellocyril user..."

# Test connection and access
if sudo -u postgres psql -d cyril_security -c "
    -- Test as hellocyril user
    SET ROLE hellocyril;
    SELECT 'Testing as hellocyril user' as status;
    SELECT COUNT(*) FROM subscription_payments;
    SELECT COUNT(*) FROM subscribers;
    RESET ROLE;
    SELECT 'Test completed successfully' as result;
"; then
    print_success "Access test as hellocyril user successful"
else
    print_warning "Access test had issues"
fi

# Final verification
print_status "Final verification..."

# Show final table ownership and permissions
sudo -u postgres psql -d cyril_security -c "
    SELECT 'Final table ownership:' as info;
    SELECT tablename, tableowner FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;
    
    SELECT 'Permission check for hellocyril:' as info;
    SELECT 
        'subscription_payments' as table_name,
        has_table_privilege('hellocyril', 'subscription_payments', 'SELECT') as can_select,
        has_table_privilege('hellocyril', 'subscription_payments', 'INSERT') as can_insert,
        has_table_privilege('hellocyril', 'subscription_payments', 'UPDATE') as can_update,
        has_table_privilege('hellocyril', 'subscription_payments', 'DELETE') as can_delete;
"

# Clean up temporary files
rm -f /tmp/reset_permissions.sql

# Restart the application
print_status "Restarting Hello Cyril application..."
sudo systemctl start hello_cyril 2>/dev/null || print_warning "Hello Cyril service not configured or failed to start"

# Final status
echo ""
echo "=================================================="
print_success "🎉 Database permission reset completed!"
echo ""
echo "📋 What was done:"
echo "   ✅ PostgreSQL service restarted"
echo "   ✅ hellocyril user created/updated with proper permissions"
echo "   ✅ All privileges granted to hellocyril user"
echo "   ✅ Table ownership changed to hellocyril"
echo "   ✅ Default privileges set for future objects"
echo "   ✅ Access tested and verified"
echo ""
echo "🔄 Next steps:"
echo "   1. Refresh your PostgreSQL GUI connection"
echo "   2. Try connecting as 'hellocyril' user with password 'local'"
echo "   3. Test accessing subscription_payments table"
echo "   4. You should now have full read/write access"
echo ""
echo "🧪 Test command:"
echo "   python3 -c \"import psycopg2; conn=psycopg2.connect(dbname='cyril_security', user='hellocyril', password='local', host='localhost'); cur=conn.cursor(); cur.execute('SELECT COUNT(*) FROM subscription_payments'); print('✅ Access working:', cur.fetchone()[0], 'records')\""
echo ""
echo "=================================================="
