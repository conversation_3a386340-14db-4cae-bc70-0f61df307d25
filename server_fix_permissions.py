#!/usr/bin/env python3
"""
Server-side PostgreSQL Permissions Fix for Hello Cyril
Run this script on your Ubuntu server to fix database permissions
"""

import psycopg2
import sys
import os
from pathlib import Path

def print_status(message):
    print(f"🔧 {message}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_warning(message):
    print(f"⚠️ {message}")

def fix_permissions_as_postgres():
    """Fix permissions by connecting as postgres superuser"""
    
    print_status("Attempting to fix permissions as postgres superuser...")
    
    try:
        # Connect as postgres superuser
        conn = psycopg2.connect(
            dbname='cyril_security',
            user='postgres',
            password='local',
            host='localhost'
        )
        conn.autocommit = True
        cur = conn.cursor()
        
        print_success("Connected as postgres superuser")
        
        # Show current tables and their owners
        print_status("Current table ownership:")
        cur.execute("""
            SELECT schemaname, tablename, tableowner 
            FROM pg_tables 
            WHERE schemaname = 'public' 
            ORDER BY tablename;
        """)
        
        tables = cur.fetchall()
        for schema, table, owner in tables:
            print(f"   {table} -> owned by {owner}")
        
        # Grant all privileges to both postgres and hellocyril users
        users_to_grant = ['postgres', 'hellocyril']
        
        for user in users_to_grant:
            print_status(f"Granting privileges to user: {user}")
            
            try:
                # Grant database privileges
                cur.execute(f"GRANT ALL PRIVILEGES ON DATABASE cyril_security TO {user};")
                print_success(f"Granted database privileges to {user}")
                
                # Grant schema privileges
                cur.execute(f"GRANT ALL PRIVILEGES ON SCHEMA public TO {user};")
                print_success(f"Granted schema privileges to {user}")
                
                # Grant table privileges
                cur.execute(f"GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO {user};")
                print_success(f"Granted table privileges to {user}")
                
                # Grant sequence privileges
                cur.execute(f"GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO {user};")
                print_success(f"Granted sequence privileges to {user}")
                
                # Set default privileges for future objects
                cur.execute(f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO {user};")
                cur.execute(f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO {user};")
                print_success(f"Set default privileges for {user}")
                
            except psycopg2.Error as e:
                print_error(f"Error granting privileges to {user}: {e}")
        
        # Change ownership of all tables to postgres
        print_status("Changing table ownership to postgres...")
        
        cur.execute("""
            SELECT tablename 
            FROM pg_tables 
            WHERE schemaname = 'public'
        """)
        
        table_names = [row[0] for row in cur.fetchall()]
        
        for table_name in table_names:
            try:
                cur.execute(f"ALTER TABLE public.{table_name} OWNER TO postgres;")
                print_success(f"Changed ownership of {table_name} to postgres")
            except psycopg2.Error as e:
                print_warning(f"Could not change ownership of {table_name}: {e}")
        
        # Change ownership of all sequences to postgres
        print_status("Changing sequence ownership to postgres...")
        
        cur.execute("""
            SELECT sequencename 
            FROM pg_sequences 
            WHERE schemaname = 'public'
        """)
        
        sequence_names = [row[0] for row in cur.fetchall()]
        
        for seq_name in sequence_names:
            try:
                cur.execute(f"ALTER SEQUENCE public.{seq_name} OWNER TO postgres;")
                print_success(f"Changed ownership of {seq_name} to postgres")
            except psycopg2.Error as e:
                print_warning(f"Could not change ownership of {seq_name}: {e}")
        
        # Test access to subscription tables
        print_status("Testing access to subscription tables...")
        
        subscription_tables = [
            'subscription_payments',
            'subscribers', 
            'api_keys',
            'alert_subscriptions',
            'api_usage_logs'
        ]
        
        for table_name in subscription_tables:
            try:
                cur.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cur.fetchone()[0]
                print_success(f"✅ {table_name}: {count} records")
            except psycopg2.Error as e:
                print_error(f"❌ {table_name}: {e}")
        
        # Show final ownership
        print_status("Final table ownership:")
        cur.execute("""
            SELECT schemaname, tablename, tableowner 
            FROM pg_tables 
            WHERE schemaname = 'public' 
            ORDER BY tablename;
        """)
        
        tables = cur.fetchall()
        for schema, table, owner in tables:
            print(f"   {table} -> owned by {owner}")
        
        cur.close()
        conn.close()
        
        print_success("Permissions fixed successfully!")
        return True
        
    except psycopg2.Error as e:
        print_error(f"Database error: {e}")
        return False
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        return False

def fix_permissions_as_hellocyril():
    """Try to fix permissions as hellocyril user"""
    
    print_status("Attempting to connect as hellocyril user...")
    
    try:
        # Connect as hellocyril user
        conn = psycopg2.connect(
            dbname='cyril_security',
            user='hellocyril',
            password='local',
            host='localhost'
        )
        conn.autocommit = True
        cur = conn.cursor()
        
        print_success("Connected as hellocyril user")
        
        # Test access to subscription tables
        print_status("Testing access to subscription tables...")
        
        subscription_tables = [
            'subscription_payments',
            'subscribers', 
            'api_keys',
            'alert_subscriptions',
            'api_usage_logs'
        ]
        
        all_accessible = True
        
        for table_name in subscription_tables:
            try:
                cur.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cur.fetchone()[0]
                print_success(f"✅ {table_name}: {count} records - ACCESSIBLE")
            except psycopg2.Error as e:
                print_error(f"❌ {table_name}: {e}")
                all_accessible = False
        
        cur.close()
        conn.close()
        
        return all_accessible
        
    except psycopg2.Error as e:
        print_error(f"Could not connect as hellocyril: {e}")
        return False

def run_system_commands():
    """Run system commands to fix PostgreSQL permissions"""
    
    print_status("Running system commands to fix PostgreSQL permissions...")
    
    commands = [
        # Restart PostgreSQL service
        "sudo systemctl restart postgresql",
        
        # Fix permissions using sudo -u postgres
        """sudo -u postgres psql -d cyril_security -c "
            GRANT ALL PRIVILEGES ON DATABASE cyril_security TO postgres;
            GRANT ALL PRIVILEGES ON DATABASE cyril_security TO hellocyril;
            GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;
            GRANT ALL PRIVILEGES ON SCHEMA public TO hellocyril;
            GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
            GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hellocyril;
            GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
            GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hellocyril;
            ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres;
            ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO hellocyril;
            ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;
            ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO hellocyril;
        \"""",
        
        # Change ownership of tables
        """sudo -u postgres psql -d cyril_security -c "
            DO \\$\\$
            DECLARE
                table_record RECORD;
            BEGIN
                FOR table_record IN 
                    SELECT tablename 
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                LOOP
                    EXECUTE 'ALTER TABLE public.' || quote_ident(table_record.tablename) || ' OWNER TO postgres';
                END LOOP;
            END \\$\\$;
        \"""",
    ]
    
    for cmd in commands:
        print_status(f"Running: {cmd[:50]}...")
        try:
            result = os.system(cmd)
            if result == 0:
                print_success("Command executed successfully")
            else:
                print_warning(f"Command returned code: {result}")
        except Exception as e:
            print_error(f"Error running command: {e}")

def main():
    print("🚀 Hello Cyril - Server PostgreSQL Permissions Fix")
    print("=" * 60)
    
    # Check if we're running on the server
    if not Path('/home/<USER>').exists():
        print_error("This script should be run on the Ubuntu server")
        print_error("Please upload this script to your server and run it there")
        sys.exit(1)
    
    print_success("Running on Ubuntu server")
    
    # Method 1: Try to fix as postgres superuser
    print("\n" + "=" * 40)
    print("METHOD 1: Fix as postgres superuser")
    print("=" * 40)
    
    success = fix_permissions_as_postgres()
    
    if success:
        print_success("Method 1 successful!")
    else:
        print_warning("Method 1 failed, trying system commands...")
        
        # Method 2: Use system commands
        print("\n" + "=" * 40)
        print("METHOD 2: System commands")
        print("=" * 40)
        
        run_system_commands()
    
    # Test final access
    print("\n" + "=" * 40)
    print("FINAL TEST: Check access as hellocyril")
    print("=" * 40)
    
    final_success = fix_permissions_as_hellocyril()
    
    if final_success:
        print("\n🎉 SUCCESS! All permissions fixed!")
        print("\n📋 You can now:")
        print("   ✅ Access all tables in PostgreSQL GUI")
        print("   ✅ Edit subscription data")
        print("   ✅ Run queries without permission errors")
        print("   ✅ Use the Hello Cyril API system")
    else:
        print("\n❌ Some issues remain. Manual intervention may be needed.")
        print("\n🛠️ Manual fix commands:")
        print("   sudo -u postgres psql")
        print("   \\c cyril_security;")
        print("   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;")
        print("   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hellocyril;")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
