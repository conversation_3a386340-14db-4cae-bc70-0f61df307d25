../../Scripts/email_validator.exe,sha256=Hxl36_dtqJun1s865f8P6oXJNDBFXRpEEx1zY0nDEJQ,108443
email_validator-2.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
email_validator-2.1.0.dist-info/LICENSE,sha256=ogEPNDSH0_dhiv_lT3ifVIdgIzHAqNA_SemnxUfPBJk,7048
email_validator-2.1.0.dist-info/METADATA,sha256=05Enu__QXkc80dt3d_ny2oA-4oWyjpGyxvD25PJaOKE,25834
email_validator-2.1.0.dist-info/RECORD,,
email_validator-2.1.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
email_validator-2.1.0.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
email_validator-2.1.0.dist-info/entry_points.txt,sha256=zRM_6bNIUSHTbNx5u6M3nK1MAguvryrc9hICC6HyrBg,66
email_validator-2.1.0.dist-info/top_level.txt,sha256=fYDOSWFZke46ut7WqdOAJjjhlpPYAaOwOwIsh3s8oWI,16
email_validator/__init__.py,sha256=_GA_uk_JuZcy6dPSVpvyyQuEPZt7iD-sXDbOd4f9sIQ,4236
email_validator/__main__.py,sha256=SgarDcfH3W5KlcuUi6aaiQPqMdL3C-mOZVnTS6WesS4,2146
email_validator/__pycache__/__init__.cpython-312.pyc,,
email_validator/__pycache__/__main__.cpython-312.pyc,,
email_validator/__pycache__/deliverability.cpython-312.pyc,,
email_validator/__pycache__/exceptions_types.cpython-312.pyc,,
email_validator/__pycache__/rfc_constants.cpython-312.pyc,,
email_validator/__pycache__/syntax.cpython-312.pyc,,
email_validator/__pycache__/validate_email.cpython-312.pyc,,
email_validator/__pycache__/version.cpython-312.pyc,,
email_validator/deliverability.py,sha256=it2lcdg-uML_-AVXOWQ6FbUW0bJjtViVEh-w2Im1E3g,5861
email_validator/exceptions_types.py,sha256=yX1-0rSQ-f7HEIy8br_dEhW_Ctw9PCtgCceUSRFEdPo,5807
email_validator/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
email_validator/rfc_constants.py,sha256=2yJVQgKFaVcG2CV-XT4PWevBUjAPVHRycgzOpBaqRZE,2720
email_validator/syntax.py,sha256=6YF_2M5oSEKgtaX6E5wFnZHCU_9sGrO-We4UReqXGME,27307
email_validator/validate_email.py,sha256=DDdTj7Xwqhq0pCCMw-fRy213SuM0kmGzu-b2I1yTRNU,6585
email_validator/version.py,sha256=Xybt2skBZamGMNlLuOX1IG-h4uIxqUDGAO8MIGWrJac,22
