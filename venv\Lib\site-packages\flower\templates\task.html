{% extends "base.html" %}

{% block navbar %}
  {% module Template("navbar.html", active_tab="tasks") %}
{% end %}

{% block container %}
  <div id='task-page' class="container-fluid mt-3">
    <div class="row-fluid">
      <div class="col-lg-12">
        <div class="page-header">
          <p id="taskid" class="d-none">{{ task.uuid }}</p>
          <h2>{{ getattr(task, 'name', None) }}
            <small class="text-muted fs-5">{{ task.uuid }}</small>
            {% if task.state == "STARTED" %}
                <button class="btn btn-danger float-end" id="task-terminate">Terminate</button>
            {% elif task.state == "RECEIVED" or task.state == "RETRY" %}
                <button class="btn btn-danger float-end" id="task-revoke">Revoke</button>
            {% end %}
          </h2>
        </div>
        <div class="row-fluid">
          <div class="col-lg-6">
            <table class="table table-bordered table-striped">
              <tbody>
              <tr>
                <td>Name</td>
                <td>{{ getattr(task, 'name', None) }}</td>
              </tr>
              <tr>
                <td>UUID</td>
                <td>{{ task.uuid }}</td>
              </tr>
              <tr>
                <td>State</td>
                <td>
                  {% if task.state == "SUCCESS" %}
                  <span class="badge bg-success">{{ task.state }}</span>
                  {% elif task.state == "FAILURE" %}
                  <span class="badge bg-danger">{{ task.state }}</span>
                  {% else %}
                  <span class="badge bg-secondary">{{ task.state }}</span>
                  {% end %}
                </td>
              </tr>
              <tr>
                <td>args</td>
                <td>{{ task.args }}</td>
              </tr>
              <tr>
                <td>kwargs</td>
                <td>{{ task.kwargs }}</td>
              </tr>
              <tr>
                <td>Result</td>
                <td>{{ getattr(task, 'result', '') }}</td>
              </tr>
              {% for name in task._fields %}
                {% if name not in ['name', 'uuid', 'state', 'args', 'kwargs', 'result'] and getattr(task, name, None) is not None %}
                <tr>
                  <td>{{ humanize(name) }}</td>
                  <td>
                    {% if name in ['sent', 'received', 'started', 'succeeded', 'retried', 'timestamp', 'failed', 'revoked'] %}
                    {{ humanize(getattr(task, name, None), type='time') }}
                    {% elif name == 'worker' %}
                    <a
                        href="{{ reverse_url('worker', task.worker.hostname) }}">{{ task.worker.hostname }}</a>
                    {% elif name == 'traceback' %}
                    <pre>{{ getattr(task, name, None) }}</pre>
                    {% elif name in ['parent_id', 'root_id'] %}
                    <a
                        href="{{ reverse_url('task', getattr(task, name, None)) }}">{{ getattr(task, name, None) }}</a>
                    {% elif name == 'children' %}
                      {% for child in getattr(task, name, {}) %}
                        <a href="{{ reverse_url('task', child.id) }}">{{ child.id }}</a>
                        <br>
                      {% end %}
                    {% else %}
                      {{ getattr(task, name, None) }}
                    {% end %}
                  </td>
                </tr>
                {% end %}
              {% end %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
{% end %}
